import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
import os
from database import DatabaseManager
from config import SystemConfig, UIConfig

class ReportsWindow:
    def __init__(self, parent=None):
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("التقارير")
        self.window.geometry("1200x650")
        self.window.configure(bg=SystemConfig.BACKGROUND_COLOR)
        
        # الخطوط من الإعدادات
        self.arabic_font = SystemConfig.ARABIC_FONT
        self.title_font = SystemConfig.TITLE_FONT
        
        # قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغيرات النموذج
        self.setup_variables()
        
        self.create_widgets()
        self.setup_layout()
        self.load_report_categories()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.report_category_var = tk.StringVar()
        self.report_type_var = tk.StringVar()
        self.from_date_var = tk.StringVar()
        self.to_date_var = tk.StringVar()
        self.format_var = tk.StringVar()
        self.include_charts_var = tk.BooleanVar()
        self.include_summary_var = tk.BooleanVar(value=True)
        
        # تعيين القيم الافتراضية
        today = date.today()
        self.from_date_var.set(today.replace(day=1).strftime("%Y-%m-%d"))  # بداية الشهر
        self.to_date_var.set(today.strftime("%Y-%m-%d"))  # اليوم
        self.format_var.set("PDF")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.window)
        
        # شريط العنوان
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="التقارير",
            font=self.title_font
        )
        
        # إطار اختيار التقرير
        self.selection_frame = ttk.LabelFrame(self.main_frame, text="اختيار التقرير", padding=10)
        
        # فئة التقرير
        ttk.Label(self.selection_frame, text="فئة التقرير:", font=self.arabic_font).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.category_combo = ttk.Combobox(
            self.selection_frame,
            textvariable=self.report_category_var,
            font=self.arabic_font,
            width=25,
            state="readonly"
        )
        self.category_combo.grid(row=0, column=1, padx=5, pady=5)
        self.category_combo.bind('<<ComboboxSelected>>', self.on_category_change)
        
        # نوع التقرير
        ttk.Label(self.selection_frame, text="نوع التقرير:", font=self.arabic_font).grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.type_combo = ttk.Combobox(
            self.selection_frame,
            textvariable=self.report_type_var,
            font=self.arabic_font,
            width=30,
            state="readonly"
        )
        self.type_combo.grid(row=0, column=3, padx=5, pady=5)
        
        # إطار المعايير
        self.criteria_frame = ttk.LabelFrame(self.main_frame, text="معايير التقرير", padding=10)
        
        # فترة التقرير
        ttk.Label(self.criteria_frame, text="من تاريخ:", font=self.arabic_font).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.from_date_entry = ttk.Entry(self.criteria_frame, textvariable=self.from_date_var, font=self.arabic_font, width=15)
        self.from_date_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.criteria_frame, text="إلى تاريخ:", font=self.arabic_font).grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.to_date_entry = ttk.Entry(self.criteria_frame, textvariable=self.to_date_var, font=self.arabic_font, width=15)
        self.to_date_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # تنسيق التقرير
        ttk.Label(self.criteria_frame, text="تنسيق التقرير:", font=self.arabic_font).grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.format_combo = ttk.Combobox(
            self.criteria_frame,
            textvariable=self.format_var,
            values=SystemConfig.SUPPORTED_FORMATS,
            font=self.arabic_font,
            width=12,
            state="readonly"
        )
        self.format_combo.grid(row=1, column=1, padx=5, pady=5)
        
        # خيارات إضافية
        self.include_charts_check = ttk.Checkbutton(
            self.criteria_frame,
            text="تضمين الرسوم البيانية",
            variable=self.include_charts_var
        )
        self.include_charts_check.grid(row=1, column=2, padx=5, pady=5)
        
        self.include_summary_check = ttk.Checkbutton(
            self.criteria_frame,
            text="تضمين الملخص",
            variable=self.include_summary_var
        )
        self.include_summary_check.grid(row=1, column=3, padx=5, pady=5)
        
        # إطار الأزرار
        self.buttons_frame = ttk.Frame(self.main_frame)
        
        self.preview_btn = ttk.Button(
            self.buttons_frame,
            text="معاينة التقرير",
            command=self.preview_report,
            width=15
        )
        
        self.generate_btn = ttk.Button(
            self.buttons_frame,
            text="إنشاء التقرير",
            command=self.generate_report,
            width=15
        )
        
        self.save_btn = ttk.Button(
            self.buttons_frame,
            text="حفظ التقرير",
            command=self.save_report,
            width=15
        )
        
        self.print_btn = ttk.Button(
            self.buttons_frame,
            text="طباعة التقرير",
            command=self.print_report,
            width=15
        )
        
        # إطار المعاينة
        self.preview_frame = ttk.LabelFrame(self.main_frame, text="معاينة التقرير", padding=10)
        
        # منطقة النص للمعاينة
        self.preview_text = tk.Text(
            self.preview_frame,
            wrap='word',
            font=self.arabic_font,
            height=15,
            width=80
        )
        
        # شريط التمرير للمعاينة
        self.preview_scrollbar = ttk.Scrollbar(self.preview_frame, orient='vertical', command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=self.preview_scrollbar.set)
        
        # إطار الحالة
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(
            self.status_frame,
            text="جاهز لإنشاء التقارير",
            font=self.arabic_font
        )
        
    def setup_layout(self):
        """ترتيب عناصر الواجهة"""
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.title_frame.pack(fill='x', pady=(0, 10))
        self.title_label.pack()
        
        self.selection_frame.pack(fill='x', pady=(0, 10))
        
        self.criteria_frame.pack(fill='x', pady=(0, 10))
        
        self.buttons_frame.pack(fill='x', pady=(0, 10))
        self.preview_btn.pack(side='left', padx=5)
        self.generate_btn.pack(side='left', padx=5)
        self.save_btn.pack(side='left', padx=5)
        self.print_btn.pack(side='left', padx=5)
        
        self.preview_frame.pack(fill='both', expand=True, pady=(0, 10))
        self.preview_text.pack(side='left', fill='both', expand=True)
        self.preview_scrollbar.pack(side='right', fill='y')
        
        self.status_frame.pack(fill='x')
        self.status_label.pack(side='left')
        
    def load_report_categories(self):
        """تحميل فئات التقارير"""
        categories = {
            "تقارير الإنتاج": [
                "تقرير أوامر التشغيل",
                "تقرير بطاقات التشغيل",
                "تقرير كفاءة الإنتاج",
                "تقرير معدلات الإنجاز"
            ],
            "تقارير التكاليف": [
                "تقرير التكاليف المعيارية",
                "تقرير التكاليف الفعلية",
                "تقرير انحرافات التكلفة",
                "تقرير تكلفة المنتجات"
            ],
            "تقارير المواد": [
                "تقرير استهلاك المواد",
                "تقرير تكلفة المواد",
                "تقرير حركة المواد",
                "تقرير المخزون"
            ],
            "تقارير الجودة": [
                "تقرير القطاعات المعيبة",
                "تقرير أسباب العيوب",
                "تقرير تكلفة العيوب",
                "تقرير معدلات الجودة"
            ],
            "تقارير الأداء": [
                "تقرير أداء الورش",
                "تقرير أداء العمليات",
                "تقرير أداء المشغلين",
                "تقرير الكفاءة العامة"
            ],
            "تقارير إدارية": [
                "تقرير ملخص الإنتاج",
                "تقرير المؤشرات الرئيسية",
                "تقرير التحليل المالي",
                "تقرير الاتجاهات"
            ]
        }
        
        self.report_categories = categories
        self.category_combo['values'] = list(categories.keys())
        
    def on_category_change(self, event=None):
        """عند تغيير فئة التقرير"""
        category = self.report_category_var.get()
        if category in self.report_categories:
            self.type_combo['values'] = self.report_categories[category]
            self.type_combo.set("")
        
    def preview_report(self):
        """معاينة التقرير"""
        if not self.validate_inputs():
            return
        
        report_type = self.report_type_var.get()
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        
        # مسح المعاينة السابقة
        self.preview_text.delete('1.0', tk.END)
        
        # إنشاء معاينة التقرير
        preview_content = self.generate_report_content(report_type, from_date, to_date, preview_mode=True)
        
        # عرض المعاينة
        self.preview_text.insert('1.0', preview_content)
        
        self.update_status(f"تم إنشاء معاينة التقرير: {report_type}")
        
    def generate_report(self):
        """إنشاء التقرير"""
        if not self.validate_inputs():
            return
        
        report_type = self.report_type_var.get()
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        
        # إنشاء محتوى التقرير
        report_content = self.generate_report_content(report_type, from_date, to_date, preview_mode=False)
        
        # عرض التقرير في المعاينة
        self.preview_text.delete('1.0', tk.END)
        self.preview_text.insert('1.0', report_content)
        
        self.update_status(f"تم إنشاء التقرير: {report_type}")
        messagebox.showinfo("نجح", "تم إنشاء التقرير بنجاح")
        
    def generate_report_content(self, report_type, from_date, to_date, preview_mode=False):
        """إنشاء محتوى التقرير"""
        content = []
        
        # رأس التقرير
        content.append("=" * 80)
        content.append(f"تقرير: {report_type}")
        content.append(f"الفترة: من {from_date} إلى {to_date}")
        content.append(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("=" * 80)
        content.append("")
        
        # محتوى التقرير حسب النوع
        if "أوامر التشغيل" in report_type:
            content.extend(self.generate_work_orders_report(from_date, to_date, preview_mode))
        elif "بطاقات التشغيل" in report_type:
            content.extend(self.generate_operation_cards_report(from_date, to_date, preview_mode))
        elif "استهلاك المواد" in report_type:
            content.extend(self.generate_material_consumption_report(from_date, to_date, preview_mode))
        elif "القطاعات المعيبة" in report_type:
            content.extend(self.generate_defective_items_report(from_date, to_date, preview_mode))
        elif "التكاليف" in report_type:
            content.extend(self.generate_costs_report(from_date, to_date, preview_mode))
        else:
            content.append("هذا النوع من التقارير قيد التطوير...")
            content.append("")
        
        # ملخص التقرير (إذا كان مطلوباً)
        if self.include_summary_var.get():
            content.append("-" * 80)
            content.append("ملخص التقرير:")
            content.append("-" * 80)
            content.append("• تم إنشاء هذا التقرير تلقائياً من نظام التكاليف الصناعي")
            content.append("• البيانات محدثة حتى تاريخ إنشاء التقرير")
            content.append("• للاستفسارات يرجى مراجعة إدارة النظام")
            content.append("")
        
        # تذييل التقرير
        content.append("=" * 80)
        content.append(f"نظام التكاليف الصناعي المتكامل - الإصدار {SystemConfig.VERSION}")
        content.append("=" * 80)
        
        return "\n".join(content)
        
    def generate_work_orders_report(self, from_date, to_date, preview_mode):
        """إنشاء تقرير أوامر التشغيل"""
        content = []
        
        query = """
        SELECT wo.order_number, p.name as product_name, wo.quantity, 
               wo.start_date, wo.end_date, wo.status
        FROM work_orders wo
        LEFT JOIN products p ON wo.product_id = p.id
        WHERE wo.start_date BETWEEN ? AND ?
        ORDER BY wo.start_date DESC
        """
        
        success, results = self.db.execute_query(query, [from_date, to_date])
        
        if success:
            content.append("تقرير أوامر التشغيل:")
            content.append("-" * 50)
            content.append("")
            
            if results:
                content.append(f"{'رقم الأمر':<15} {'المنتج':<20} {'الكمية':<10} {'تاريخ البداية':<12} {'تاريخ الانتهاء':<12} {'الحالة':<10}")
                content.append("-" * 90)
                
                for row in results:
                    content.append(f"{row[0]:<15} {row[1]:<20} {row[2]:<10} {row[3]:<12} {row[4]:<12} {row[5]:<10}")
                
                content.append("")
                content.append(f"إجمالي عدد الأوامر: {len(results)}")
            else:
                content.append("لا توجد أوامر تشغيل في الفترة المحددة.")
        else:
            content.append("خطأ في جلب بيانات أوامر التشغيل.")
        
        content.append("")
        return content
        
    def generate_operation_cards_report(self, from_date, to_date, preview_mode):
        """إنشاء تقرير بطاقات التشغيل"""
        content = []
        content.append("تقرير بطاقات التشغيل قيد التطوير...")
        content.append("")
        return content
        
    def generate_material_consumption_report(self, from_date, to_date, preview_mode):
        """إنشاء تقرير استهلاك المواد"""
        content = []
        content.append("تقرير استهلاك المواد قيد التطوير...")
        content.append("")
        return content
        
    def generate_defective_items_report(self, from_date, to_date, preview_mode):
        """إنشاء تقرير القطاعات المعيبة"""
        content = []
        content.append("تقرير القطاعات المعيبة قيد التطوير...")
        content.append("")
        return content
        
    def generate_costs_report(self, from_date, to_date, preview_mode):
        """إنشاء تقرير التكاليف"""
        content = []
        content.append("تقرير التكاليف قيد التطوير...")
        content.append("")
        return content
        
    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        if not self.report_type_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار نوع التقرير")
            return False
        
        try:
            from_date = datetime.strptime(self.from_date_var.get(), "%Y-%m-%d")
            to_date = datetime.strptime(self.to_date_var.get(), "%Y-%m-%d")
            
            if to_date < from_date:
                messagebox.showerror("خطأ", "تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية")
                return False
        except:
            messagebox.showerror("خطأ", "تنسيق التاريخ يجب أن يكون YYYY-MM-DD")
            return False
        
        return True
        
    def save_report(self):
        """حفظ التقرير"""
        content = self.preview_text.get('1.0', tk.END).strip()
        if not content:
            messagebox.showwarning("تحذير", "لا يوجد تقرير لحفظه")
            return
        
        # اختيار مكان الحفظ
        file_path = filedialog.asksaveasfilename(
            title="حفظ التقرير",
            defaultextension=".txt",
            filetypes=[
                ("ملفات نصية", "*.txt"),
                ("جميع الملفات", "*.*")
            ]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(content)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{file_path}")
                self.update_status(f"تم حفظ التقرير في: {file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ التقرير:\n{e}")
        
    def print_report(self):
        """طباعة التقرير"""
        content = self.preview_text.get('1.0', tk.END).strip()
        if not content:
            messagebox.showwarning("تحذير", "لا يوجد تقرير للطباعة")
            return
        
        messagebox.showinfo("معلومات", "وظيفة الطباعة قيد التطوير")
        
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)

if __name__ == "__main__":
    app = ReportsWindow()
    app.window.mainloop()

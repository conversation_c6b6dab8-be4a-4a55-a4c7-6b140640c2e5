#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏭 نظام التكاليف الصناعي المتكامل - النسخة المحدثة
Industrial Cost Management System - Updated Version

ملف التشغيل السريع للنسخة المحدثة
Quick start file for the updated version
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🏭 نظام التكاليف الصناعي المتكامل")
print("=" * 50)
print("النسخة المحدثة 2.0")
print("=" * 50)

try:
    # تشغيل النسخة المحدثة مباشرة
    from modern_main_window import ModernMainWindow
    from database import DatabaseManager
    
    # تهيئة قاعدة البيانات
    db = DatabaseManager()
    db.initialize_database()
    
    # تشغيل النظام
    app = ModernMainWindow()
    app.run()
    
except:
    # في حالة فشل النسخة المودرن، تشغيل النسخة الكلاسيكية
    print("⚠️ تعذر تحميل الواجهة المودرن")
    print("🔄 جاري تحميل الواجهة الكلاسيكية...")
    
    try:
        from main_window import MainWindow
        from database import DatabaseManager
        
        db = DatabaseManager()
        db.initialize_database()
        
        app = MainWindow()
        app.run()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

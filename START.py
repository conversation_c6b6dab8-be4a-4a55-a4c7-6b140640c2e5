#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏭 نظام التكاليف الصناعي المتكامل - النسخة المحدثة
Industrial Cost Management System - Updated Version

ملف التشغيل السريع للنسخة المحدثة
Quick start file for the updated version
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Industrial Cost Management System")
print("=" * 50)
print("Updated Version 2.0")
print("=" * 50)

try:
    # تشغيل النسخة المحدثة مباشرة
    from modern_main_window import ModernMainWindow
    from database import DatabaseManager
    
    # تهيئة قاعدة البيانات
    db = DatabaseManager()
    db.initialize_database()
    
    # تشغيل النظام
    app = ModernMainWindow()
    app.run()
    
except:
    # في حالة فشل النسخة المودرن، تشغيل النسخة الكلاسيكية
    print("Warning: Could not load modern interface")
    print("Loading classic interface...")

    try:
        from main_window import MainWindow
        from database import DatabaseManager

        db = DatabaseManager()
        db.initialize_database()

        app = MainWindow()
        app.run()

    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

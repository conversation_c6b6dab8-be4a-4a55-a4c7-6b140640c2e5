========================================
Industrial Cost Management System
Quick Start Guide
========================================

UPDATED VERSION 2.0 - NEW FEATURES:
- Product-Parts Linking (BOM)
- Modern Professional Interface
- Enhanced Navigation
- Interactive Dashboard

========================================
HOW TO RUN THE SYSTEM:
========================================

METHOD 1: Double-click on file
   start.bat

METHOD 2: Command line
   python run.py

METHOD 3: Alternative
   python START.py

METHOD 4: System check
   python check_system.py

========================================
TROUBLESHOOTING:
========================================

If you get errors:
1. Make sure Python is installed
2. Make sure all files are in same folder
3. Try: py run.py instead of python run.py

Required files:
- run.py
- database.py
- config.py
- modern_main_window.py
- modern_styles.py
- product_parts_window.py
- All other window files

========================================
SYSTEM REQUIREMENTS:
========================================

- Windows 7+ (32/64 bit)
- Python 3.6 or newer
- No additional libraries needed

========================================
SUPPORT:
========================================

If you have problems:
1. Run: python check_system.py
2. Check error messages
3. Make sure all files are present

========================================

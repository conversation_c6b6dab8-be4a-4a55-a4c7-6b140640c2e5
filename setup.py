#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد التوزيع لنظام التكاليف الصناعي المتكامل
Setup script for Industrial Cost Management System
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    try:
        with open('README.md', 'r', encoding='utf-8') as f:
            return f.read()
    except:
        return "نظام التكاليف الصناعي المتكامل"

# قراءة متطلبات النظام
def read_requirements():
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            # تصفية التعليقات والأسطر الفارغة
            requirements = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    requirements.append(line)
            return requirements
    except:
        return []

# معلومات النظام
SYSTEM_INFO = {
    'name': 'industrial-cost-system',
    'version': '1.0.0',
    'description': 'نظام التكاليف الصناعي المتكامل - Industrial Cost Management System',
    'long_description': read_readme(),
    'long_description_content_type': 'text/markdown',
    'author': 'فريق تطوير النظام',
    'author_email': '<EMAIL>',
    'url': 'https://github.com/industrial-cost-system',
    'license': 'MIT',
    'classifiers': [
        'Development Status :: 4 - Beta',
        'Intended Audience :: Manufacturing',
        'Topic :: Office/Business :: Financial :: Accounting',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.6',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Operating System :: Microsoft :: Windows',
        'Natural Language :: Arabic',
    ],
    'keywords': 'accounting manufacturing cost management industrial',
    'python_requires': '>=3.6',
}

# الملفات المطلوبة
PACKAGE_DATA = {
    '': [
        '*.md',
        '*.txt',
        '*.bat',
        '*.py',
        '*.png',
        '*.jpg',
        '*.ico',
    ]
}

# البيانات الإضافية
DATA_FILES = [
    ('docs', ['README.md', 'دليل_المستخدم.md']),
    ('scripts', ['تشغيل_النظام.bat', 'install.py']),
    ('config', ['requirements.txt']),
]

# نقاط الدخول
ENTRY_POINTS = {
    'console_scripts': [
        'industrial-cost=run_system:main',
        'cost-system=run_system:main',
        'test-cost-system=test_system:run_all_tests',
    ],
    'gui_scripts': [
        'industrial-cost-gui=run_system:main',
    ]
}

def setup_system():
    """إعداد النظام للتوزيع"""
    
    setup(
        name=SYSTEM_INFO['name'],
        version=SYSTEM_INFO['version'],
        description=SYSTEM_INFO['description'],
        long_description=SYSTEM_INFO['long_description'],
        long_description_content_type=SYSTEM_INFO['long_description_content_type'],
        author=SYSTEM_INFO['author'],
        author_email=SYSTEM_INFO['author_email'],
        url=SYSTEM_INFO['url'],
        license=SYSTEM_INFO['license'],
        
        # الحزم والوحدات
        packages=find_packages(),
        py_modules=[
            'run_system',
            'main_window', 
            'database',
            'products_window',
            'config',
            'test_system',
            'install'
        ],
        
        # البيانات
        package_data=PACKAGE_DATA,
        data_files=DATA_FILES,
        include_package_data=True,
        
        # المتطلبات
        install_requires=read_requirements(),
        python_requires=SYSTEM_INFO['python_requires'],
        
        # التصنيفات
        classifiers=SYSTEM_INFO['classifiers'],
        keywords=SYSTEM_INFO['keywords'],
        
        # نقاط الدخول
        entry_points=ENTRY_POINTS,
        
        # إعدادات إضافية
        zip_safe=False,
        platforms=['Windows'],
        
        # معلومات المشروع
        project_urls={
            'Documentation': 'https://github.com/industrial-cost-system/docs',
            'Source': 'https://github.com/industrial-cost-system',
            'Tracker': 'https://github.com/industrial-cost-system/issues',
        },
    )

def create_manifest():
    """إنشاء ملف MANIFEST.in"""
    manifest_content = """
# ملف MANIFEST.in لنظام التكاليف الصناعي المتكامل

# الملفات الأساسية
include *.py
include *.md
include *.txt
include *.bat

# الصور
include *.PNG
include *.png
include *.jpg
include *.jpeg
include *.ico

# ملفات التكوين
include config.py
include requirements.txt

# الوثائق
include README.md
include دليل_المستخدم.md

# المجلدات
recursive-include backups *
recursive-include reports *
recursive-include exports *
recursive-include logs *

# استبعاد الملفات غير المرغوبة
global-exclude *.pyc
global-exclude *.pyo
global-exclude *~
global-exclude .git*
global-exclude __pycache__
global-exclude *.db
"""
    
    try:
        with open('MANIFEST.in', 'w', encoding='utf-8') as f:
            f.write(manifest_content.strip())
        print("✅ تم إنشاء ملف MANIFEST.in")
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف MANIFEST.in: {e}")

def create_pyproject_toml():
    """إنشاء ملف pyproject.toml"""
    pyproject_content = """
[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "industrial-cost-system"
version = "1.0.0"
description = "نظام التكاليف الصناعي المتكامل - Industrial Cost Management System"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "فريق تطوير النظام", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Manufacturing",
    "Topic :: Office/Business :: Financial :: Accounting",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.6",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Operating System :: Microsoft :: Windows",
    "Natural Language :: Arabic",
]
keywords = ["accounting", "manufacturing", "cost", "management", "industrial"]
requires-python = ">=3.6"

[project.urls]
Homepage = "https://github.com/industrial-cost-system"
Documentation = "https://github.com/industrial-cost-system/docs"
Repository = "https://github.com/industrial-cost-system"
"Bug Tracker" = "https://github.com/industrial-cost-system/issues"

[project.scripts]
industrial-cost = "run_system:main"
cost-system = "run_system:main"
test-cost-system = "test_system:run_all_tests"

[project.gui-scripts]
industrial-cost-gui = "run_system:main"

[tool.setuptools]
packages = ["industrial_cost_system"]
include-package-data = true

[tool.setuptools.package-data]
"*" = ["*.md", "*.txt", "*.bat", "*.png", "*.jpg", "*.ico"]
"""
    
    try:
        with open('pyproject.toml', 'w', encoding='utf-8') as f:
            f.write(pyproject_content.strip())
        print("✅ تم إنشاء ملف pyproject.toml")
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف pyproject.toml: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 إعداد نظام التكاليف الصناعي المتكامل للتوزيع")
    print("=" * 50)
    
    # إنشاء الملفات المطلوبة
    create_manifest()
    create_pyproject_toml()
    
    # تشغيل الإعداد
    try:
        setup_system()
        print("\n✅ تم إعداد النظام للتوزيع بنجاح!")
        print("\n📦 لإنشاء حزمة التوزيع:")
        print("   python setup.py sdist bdist_wheel")
        print("\n📤 لرفع الحزمة:")
        print("   twine upload dist/*")
        
    except Exception as e:
        print(f"\n❌ فشل في إعداد النظام: {e}")

if __name__ == "__main__":
    main()

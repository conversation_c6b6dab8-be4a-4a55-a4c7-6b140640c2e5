#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التكاليف الصناعي المتكامل
Industrial Cost Management System

الملف الرئيسي لتشغيل النظام
Main application entry point

المطور: فريق تطوير النظام
التاريخ: 2024-12-19
الإصدار: 1.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from modern_main_window import ModernMainWindow
    from main_window import MainWindow
    from database import DatabaseManager
    from config import SystemConfig
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

def check_system_requirements():
    """فحص متطلبات النظام"""
    try:
        # فحص إصدار Python
        if sys.version_info < (3, 6):
            messagebox.showerror(
                "خطأ في النظام",
                "يتطلب النظام Python 3.6 أو أحدث\n"
                f"الإصدار الحالي: {sys.version}"
            )
            return False
        
        # فحص وجود tkinter
        try:
            import tkinter
        except ImportError:
            messagebox.showerror(
                "خطأ في النظام",
                "مكتبة tkinter غير متوفرة\n"
                "يرجى تثبيت Python مع دعم tkinter"
            )
            return False
        
        return True
        
    except Exception as e:
        print(f"خطأ في فحص متطلبات النظام: {e}")
        return False

def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        db = DatabaseManager()
        success, message = db.initialize_database()
        
        if not success:
            messagebox.showerror(
                "خطأ في قاعدة البيانات",
                f"فشل في تهيئة قاعدة البيانات:\n{message}"
            )
            return False
        
        return True
        
    except Exception as e:
        messagebox.showerror(
            "خطأ في قاعدة البيانات",
            f"حدث خطأ أثناء تهيئة قاعدة البيانات:\n{e}"
        )
        return False

def show_splash_screen():
    """عرض شاشة البداية"""
    if not SystemConfig.SHOW_SPLASH_SCREEN:
        return
    
    try:
        splash = tk.Tk()
        splash.title("نظام التكاليف الصناعي")
        splash.geometry("400x300")
        splash.configure(bg='#2c3e50')
        splash.resizable(False, False)
        
        # توسيط النافذة
        splash.eval('tk::PlaceWindow . center')
        
        # إزالة شريط العنوان
        splash.overrideredirect(True)
        
        # محتوى شاشة البداية
        main_frame = tk.Frame(splash, bg='#2c3e50')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # عنوان النظام
        title_label = tk.Label(
            main_frame,
            text=SystemConfig.SYSTEM_NAME,
            font=('Arial', 18, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=(20, 10))
        
        # العنوان الإنجليزي
        subtitle_label = tk.Label(
            main_frame,
            text=SystemConfig.SYSTEM_NAME_EN,
            font=('Arial', 12),
            fg='#bdc3c7',
            bg='#2c3e50'
        )
        subtitle_label.pack(pady=(0, 20))
        
        # معلومات الإصدار
        version_label = tk.Label(
            main_frame,
            text=f"الإصدار {SystemConfig.VERSION}",
            font=('Arial', 10),
            fg='#95a5a6',
            bg='#2c3e50'
        )
        version_label.pack(pady=(0, 10))
        
        # رسالة التحميل
        loading_label = tk.Label(
            main_frame,
            text="جاري تحميل النظام...",
            font=('Arial', 10),
            fg='#3498db',
            bg='#2c3e50'
        )
        loading_label.pack(pady=(20, 0))
        
        # شريط التقدم (مؤشر بصري)
        progress_frame = tk.Frame(main_frame, bg='#2c3e50')
        progress_frame.pack(pady=(10, 20))
        
        progress_bar = tk.Frame(progress_frame, bg='#3498db', height=3, width=200)
        progress_bar.pack()
        
        # تحديث الشاشة
        splash.update()
        
        # انتظار لفترة محددة
        splash.after(SystemConfig.SPLASH_DURATION_SECONDS * 1000, splash.destroy)
        splash.mainloop()
        
    except Exception as e:
        print(f"خطأ في شاشة البداية: {e}")

def handle_exception(exc_type, exc_value, exc_traceback):
    """معالج الأخطاء العام"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    
    messagebox.showerror(
        "خطأ في النظام",
        f"حدث خطأ غير متوقع:\n\n{exc_value}\n\n"
        "يرجى إعادة تشغيل النظام أو الاتصال بالدعم الفني."
    )
    
    # كتابة الخطأ في ملف السجل
    try:
        log_dir = SystemConfig.get_logs_path()
        log_file = os.path.join(log_dir, "error.log")
        
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"\n{'='*50}\n")
            f.write(f"تاريخ الخطأ: {tk.datetime.now()}\n")
            f.write(f"نوع الخطأ: {exc_type.__name__}\n")
            f.write(f"رسالة الخطأ: {exc_value}\n")
            f.write(f"تفاصيل الخطأ:\n{error_msg}\n")
            f.write(f"{'='*50}\n")
    except:
        pass  # تجاهل أخطاء كتابة السجل

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    try:
        # تعيين معالج الأخطاء العام
        sys.excepthook = handle_exception
        
        # فحص متطلبات النظام
        if not check_system_requirements():
            return
        
        # عرض شاشة البداية
        show_splash_screen()
        
        # تهيئة قاعدة البيانات
        if not initialize_database():
            return
        
        # إنشاء وتشغيل النافذة الرئيسية المودرن
        app = ModernMainWindow()
        app.run()
        
    except Exception as e:
        messagebox.showerror(
            "خطأ في بدء التشغيل",
            f"فشل في بدء تشغيل النظام:\n{e}"
        )
        sys.exit(1)

if __name__ == "__main__":
    main()

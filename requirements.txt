# متطلبات نظام التكاليف الصناعي المتكامل
# Industrial Cost Management System Requirements

# المكتبات الأساسية (مدمجة مع Python)
# tkinter - واجهة المستخدم الرسومية (مدمجة)
# sqlite3 - قاعدة البيانات (مدمجة)
# datetime - التعامل مع التواريخ (مدمجة)
# os - عمليات نظام التشغيل (مدمجة)
# sys - معلومات النظام (مدمجة)
# shutil - عمليات الملفات (مدمجة)
# traceback - تتبع الأخطاء (مدمجة)

# المكتبات الاختيارية للتطوير المستقبلي
# reportlab>=3.6.0  # لإنشاء تقارير PDF
# matplotlib>=3.5.0  # للرسوم البيانية
# pandas>=1.3.0  # لمعالجة البيانات
# openpyxl>=3.0.0  # للتعامل مع ملفات Excel
# pillow>=8.0.0  # لمعالجة الصور

# ملاحظات:
# 1. النظام الحالي يعتمد فقط على المكتبات المدمجة مع Python
# 2. لا يحتاج تثبيت مكتبات إضافية للتشغيل الأساسي
# 3. المكتبات المعلقة أعلاه للتطوير المستقبلي فقط
# 4. لتثبيت المكتبات الاختيارية: pip install -r requirements.txt

# متطلبات النظام:
# Python >= 3.6
# Windows 7 أو أحدث
# 100 MB مساحة فارغة
# 512 MB ذاكرة عشوائية

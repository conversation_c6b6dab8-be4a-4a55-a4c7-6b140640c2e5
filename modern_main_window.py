import tkinter as tk
from tkinter import ttk, messagebox
import os
from datetime import datetime

from modern_styles import ModernStyles
from config import SystemConfig, UIConfig
from database import DatabaseManager

# استيراد النوافذ
from products_window import ProductsWindow
from parts_window import PartsWindow
from sectors_window import SectorsWindow
from workshops_window import WorkshopsWindow
from operations_window import OperationsWindow
from part_operations_window import PartOperationsWindow
from product_parts_window import ProductPartsWindow
from work_orders_window import WorkOrdersWindow
from operation_cards_window import OperationCardsWindow
from material_issues_window import MaterialIssuesWindow
from defective_items_window import DefectiveItemsWindow
from queries_window import QueriesWindow
from reports_window import ReportsWindow

class ModernMainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(SystemConfig.SYSTEM_NAME)
        self.root.geometry(f"{UIConfig.WINDOW_SIZES['main'][0]}x{UIConfig.WINDOW_SIZES['main'][1]}")
        self.root.configure(bg=UIConfig.MODERN_COLORS['light'])
        
        # تطبيق الأنماط المودرن
        ModernStyles.configure_ttk_styles(self.root)
        
        # قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغيرات النظام
        self.current_module = None
        self.sidebar_expanded = True
        
        self.create_layout()
        self.create_sidebar()
        self.create_main_content()
        self.create_status_bar()
        self.show_dashboard()
        
        # ربط الأحداث
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_layout(self):
        """إنشاء التخطيط الأساسي"""
        # الإطار الرئيسي
        self.main_container = tk.Frame(self.root, bg=UIConfig.MODERN_COLORS['light'])
        self.main_container.pack(fill='both', expand=True)
        
        # شريط العنوان المخصص
        self.create_title_bar()
        
        # الحاوية الرئيسية للمحتوى
        self.content_container = tk.Frame(self.main_container, bg=UIConfig.MODERN_COLORS['light'])
        self.content_container.pack(fill='both', expand=True)
        
    def create_title_bar(self):
        """إنشاء شريط العنوان المخصص"""
        self.title_bar = tk.Frame(self.main_container, 
                                 bg=UIConfig.MODERN_COLORS['primary'], 
                                 height=60)
        self.title_bar.pack(fill='x')
        self.title_bar.pack_propagate(False)
        
        # أيقونة النظام والعنوان
        title_frame = tk.Frame(self.title_bar, bg=UIConfig.MODERN_COLORS['primary'])
        title_frame.pack(side='left', fill='y', padx=20)
        
        # أيقونة النظام
        system_icon = tk.Label(title_frame, 
                              text=UIConfig.ICONS['dashboard'],
                              bg=UIConfig.MODERN_COLORS['primary'],
                              fg=UIConfig.MODERN_COLORS['white'],
                              font=('Segoe UI', 20))
        system_icon.pack(side='left', pady=15)
        
        # عنوان النظام
        system_title = tk.Label(title_frame,
                               text=SystemConfig.SYSTEM_NAME,
                               bg=UIConfig.MODERN_COLORS['primary'],
                               fg=UIConfig.MODERN_COLORS['white'],
                               font=UIConfig.MODERN_FONTS['arabic_subtitle'])
        system_title.pack(side='left', padx=(10, 0), pady=15)
        
        # معلومات المستخدم والوقت
        user_frame = tk.Frame(self.title_bar, bg=UIConfig.MODERN_COLORS['primary'])
        user_frame.pack(side='right', fill='y', padx=20)
        
        # الوقت الحالي
        self.time_label = tk.Label(user_frame,
                                  text=datetime.now().strftime("%Y-%m-%d %H:%M"),
                                  bg=UIConfig.MODERN_COLORS['primary'],
                                  fg=UIConfig.MODERN_COLORS['white'],
                                  font=UIConfig.MODERN_FONTS['small'])
        self.time_label.pack(side='right', pady=(10, 5))
        
        # معلومات المستخدم
        user_label = tk.Label(user_frame,
                             text=f"{UIConfig.ICONS['user']} مدير النظام",
                             bg=UIConfig.MODERN_COLORS['primary'],
                             fg=UIConfig.MODERN_COLORS['white'],
                             font=UIConfig.MODERN_FONTS['body'])
        user_label.pack(side='right', pady=(5, 10))
        
        # تحديث الوقت كل دقيقة
        self.update_time()
        
    def update_time(self):
        """تحديث الوقت في شريط العنوان"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.time_label.config(text=current_time)
        self.root.after(60000, self.update_time)  # تحديث كل دقيقة
        
    def create_sidebar(self):
        """إنشاء الشريط الجانبي للتنقل"""
        # الشريط الجانبي
        self.sidebar = tk.Frame(self.content_container,
                               bg=UIConfig.MODERN_COLORS['dark'],
                               width=280)
        self.sidebar.pack(side='left', fill='y')
        self.sidebar.pack_propagate(False)
        
        # زر طي/فتح الشريط الجانبي
        toggle_frame = tk.Frame(self.sidebar, bg=UIConfig.MODERN_COLORS['dark'], height=50)
        toggle_frame.pack(fill='x')
        toggle_frame.pack_propagate(False)
        
        self.toggle_btn = tk.Button(toggle_frame,
                                   text="☰",
                                   bg=UIConfig.MODERN_COLORS['dark'],
                                   fg=UIConfig.MODERN_COLORS['white'],
                                   font=('Segoe UI', 16),
                                   border=0,
                                   command=self.toggle_sidebar)
        self.toggle_btn.pack(pady=10)
        
        # قائمة التنقل
        self.nav_frame = tk.Frame(self.sidebar, bg=UIConfig.MODERN_COLORS['dark'])
        self.nav_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إنشاء أقسام التنقل
        self.create_nav_sections()
        
    def create_nav_sections(self):
        """إنشاء أقسام التنقل"""
        # قسم لوحة المعلومات
        self.create_nav_section("لوحة المعلومات", [
            ("لوحة المعلومات", UIConfig.ICONS['dashboard'], self.show_dashboard)
        ])
        
        # قسم البيانات الأساسية
        self.create_nav_section("البيانات الأساسية", [
            ("المنتجات", UIConfig.ICONS['products'], self.open_products),
            ("الأجزاء", UIConfig.ICONS['parts'], self.open_parts),
            ("القطاعات", "🏢", self.open_sectors),
            ("الورش", "🏭", self.open_workshops),
            ("العمليات", UIConfig.ICONS['operations'], self.open_operations),
            ("ربط الأجزاء بالعمليات", "🔗", self.open_part_operations),
            ("ربط المنتجات بالأجزاء", "📋", self.open_product_parts)
        ])
        
        # قسم بيانات الحركة
        self.create_nav_section("بيانات الحركة", [
            ("أوامر التشغيل", UIConfig.ICONS['orders'], self.open_work_orders),
            ("بطاقات التشغيل", "📝", self.open_operation_cards),
            ("صرف المواد", UIConfig.ICONS['materials'], self.open_material_issues),
            ("القطاعات المعيبة", "⚠️", self.open_defective_items)
        ])
        
        # قسم التقارير والاستعلامات
        self.create_nav_section("التقارير والاستعلامات", [
            ("الاستعلامات", "🔍", self.open_queries),
            ("التقارير", UIConfig.ICONS['reports'], self.open_reports)
        ])
        
        # قسم النظام
        self.create_nav_section("النظام", [
            ("النسخ الاحتياطي", "💾", self.backup_database),
            ("الإعدادات", UIConfig.ICONS['settings'], self.show_settings),
            ("حول النظام", UIConfig.ICONS['help'], self.show_about)
        ])
        
    def create_nav_section(self, title, items):
        """إنشاء قسم في التنقل"""
        # عنوان القسم
        section_title = tk.Label(self.nav_frame,
                                text=title,
                                bg=UIConfig.MODERN_COLORS['dark'],
                                fg=UIConfig.MODERN_COLORS['gray_400'],
                                font=UIConfig.MODERN_FONTS['arabic_small'],
                                anchor='w')
        section_title.pack(fill='x', pady=(15, 5))
        
        # عناصر القسم
        for item_text, icon, command in items:
            self.create_nav_item(item_text, icon, command)
            
    def create_nav_item(self, text, icon, command):
        """إنشاء عنصر تنقل"""
        item_frame = tk.Frame(self.nav_frame, bg=UIConfig.MODERN_COLORS['dark'])
        item_frame.pack(fill='x', pady=2)
        
        item_btn = tk.Button(item_frame,
                            text=f"{icon} {text}",
                            bg=UIConfig.MODERN_COLORS['dark'],
                            fg=UIConfig.MODERN_COLORS['white'],
                            font=UIConfig.MODERN_FONTS['arabic_body'],
                            anchor='w',
                            border=0,
                            padx=15,
                            pady=8,
                            command=command)
        item_btn.pack(fill='x')
        
        # تأثير التمرير
        def on_enter(event):
            item_btn.config(bg=UIConfig.MODERN_COLORS['gray_700'])
            
        def on_leave(event):
            item_btn.config(bg=UIConfig.MODERN_COLORS['dark'])
            
        item_btn.bind("<Enter>", on_enter)
        item_btn.bind("<Leave>", on_leave)
        
    def toggle_sidebar(self):
        """طي/فتح الشريط الجانبي"""
        if self.sidebar_expanded:
            self.sidebar.config(width=60)
            self.sidebar_expanded = False
        else:
            self.sidebar.config(width=280)
            self.sidebar_expanded = True
            
    def create_main_content(self):
        """إنشاء منطقة المحتوى الرئيسي"""
        self.main_content = tk.Frame(self.content_container,
                                    bg=UIConfig.MODERN_COLORS['gray_100'])
        self.main_content.pack(side='right', fill='both', expand=True)
        
        # شريط التنقل العلوي (Breadcrumb)
        self.breadcrumb_frame = tk.Frame(self.main_content,
                                        bg=UIConfig.MODERN_COLORS['white'],
                                        height=50)
        self.breadcrumb_frame.pack(fill='x', padx=20, pady=(20, 10))
        self.breadcrumb_frame.pack_propagate(False)
        
        self.breadcrumb_label = tk.Label(self.breadcrumb_frame,
                                        text="الرئيسية > لوحة المعلومات",
                                        bg=UIConfig.MODERN_COLORS['white'],
                                        fg=UIConfig.MODERN_COLORS['gray_600'],
                                        font=UIConfig.MODERN_FONTS['arabic_body'],
                                        anchor='w')
        self.breadcrumb_label.pack(side='left', pady=15, padx=20)
        
        # منطقة المحتوى
        self.content_area = tk.Frame(self.main_content,
                                    bg=UIConfig.MODERN_COLORS['gray_100'])
        self.content_area.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Frame(self.main_container,
                                  bg=UIConfig.MODERN_COLORS['gray_200'],
                                  height=30)
        self.status_bar.pack(fill='x', side='bottom')
        self.status_bar.pack_propagate(False)
        
        # حالة النظام
        self.status_label = tk.Label(self.status_bar,
                                    text="جاهز",
                                    bg=UIConfig.MODERN_COLORS['gray_200'],
                                    fg=UIConfig.MODERN_COLORS['gray_700'],
                                    font=UIConfig.MODERN_FONTS['arabic_small'],
                                    anchor='w')
        self.status_label.pack(side='left', padx=20, pady=5)
        
        # معلومات قاعدة البيانات
        db_status = tk.Label(self.status_bar,
                            text=f"قاعدة البيانات: {SystemConfig.DATABASE_NAME}",
                            bg=UIConfig.MODERN_COLORS['gray_200'],
                            fg=UIConfig.MODERN_COLORS['gray_600'],
                            font=UIConfig.MODERN_FONTS['arabic_small'])
        db_status.pack(side='right', padx=20, pady=5)
        
    def clear_content_area(self):
        """مسح منطقة المحتوى"""
        for widget in self.content_area.winfo_children():
            widget.destroy()
            
    def update_breadcrumb(self, path):
        """تحديث مسار التنقل"""
        self.breadcrumb_label.config(text=f"الرئيسية > {path}")
        
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        
    def show_dashboard(self):
        """عرض لوحة المعلومات"""
        self.clear_content_area()
        self.update_breadcrumb("لوحة المعلومات")
        self.current_module = "dashboard"
        
        # إنشاء لوحة المعلومات
        dashboard_frame = tk.Frame(self.content_area, bg=UIConfig.MODERN_COLORS['gray_100'])
        dashboard_frame.pack(fill='both', expand=True)
        
        # عنوان لوحة المعلومات
        title_label = tk.Label(dashboard_frame,
                              text="لوحة المعلومات",
                              bg=UIConfig.MODERN_COLORS['gray_100'],
                              fg=UIConfig.MODERN_COLORS['dark'],
                              font=UIConfig.MODERN_FONTS['arabic_title'])
        title_label.pack(pady=20)
        
        # بطاقات الإحصائيات
        stats_frame = tk.Frame(dashboard_frame, bg=UIConfig.MODERN_COLORS['gray_100'])
        stats_frame.pack(fill='x', padx=20, pady=20)
        
        # إحصائيات سريعة
        self.create_stat_cards(stats_frame)
        
        # الرسوم البيانية والجداول
        charts_frame = tk.Frame(dashboard_frame, bg=UIConfig.MODERN_COLORS['gray_100'])
        charts_frame.pack(fill='both', expand=True, padx=20)
        
        self.create_dashboard_charts(charts_frame)
        
    def create_stat_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        # جلب الإحصائيات من قاعدة البيانات
        stats = self.get_system_statistics()
        
        # بطاقة المنتجات
        self.create_stat_card(parent, "المنتجات", stats.get('products', 0), 
                             UIConfig.ICONS['products'], UIConfig.MODERN_COLORS['primary'])
        
        # بطاقة أوامر التشغيل
        self.create_stat_card(parent, "أوامر التشغيل", stats.get('work_orders', 0),
                             UIConfig.ICONS['orders'], UIConfig.MODERN_COLORS['info'])
        
        # بطاقة العمليات المكتملة
        self.create_stat_card(parent, "العمليات المكتملة", stats.get('completed_operations', 0),
                             UIConfig.ICONS['quality'], UIConfig.MODERN_COLORS['success'])
        
        # بطاقة القطاعات المعيبة
        self.create_stat_card(parent, "القطاعات المعيبة", stats.get('defective_items', 0),
                             "⚠️", UIConfig.MODERN_COLORS['warning'])
        
    def create_stat_card(self, parent, title, value, icon, color):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=UIConfig.MODERN_COLORS['white'], 
                             relief='flat', bd=1)
        card_frame.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        
        # الأيقونة
        icon_label = tk.Label(card_frame,
                             text=icon,
                             bg=UIConfig.MODERN_COLORS['white'],
                             fg=color,
                             font=('Segoe UI', 24))
        icon_label.pack(pady=(20, 10))
        
        # القيمة
        value_label = tk.Label(card_frame,
                              text=str(value),
                              bg=UIConfig.MODERN_COLORS['white'],
                              fg=UIConfig.MODERN_COLORS['dark'],
                              font=UIConfig.MODERN_FONTS['title'])
        value_label.pack()
        
        # العنوان
        title_label = tk.Label(card_frame,
                              text=title,
                              bg=UIConfig.MODERN_COLORS['white'],
                              fg=UIConfig.MODERN_COLORS['gray_600'],
                              font=UIConfig.MODERN_FONTS['arabic_body'])
        title_label.pack(pady=(5, 20))
        
    def create_dashboard_charts(self, parent):
        """إنشاء الرسوم البيانية في لوحة المعلومات"""
        # إطار للرسوم البيانية
        charts_container = tk.Frame(parent, bg=UIConfig.MODERN_COLORS['gray_100'])
        charts_container.pack(fill='both', expand=True)
        
        # رسم بياني للإنتاج الشهري
        production_frame = tk.Frame(charts_container, bg=UIConfig.MODERN_COLORS['white'])
        production_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        production_title = tk.Label(production_frame,
                                   text="الإنتاج الشهري",
                                   bg=UIConfig.MODERN_COLORS['white'],
                                   fg=UIConfig.MODERN_COLORS['dark'],
                                   font=UIConfig.MODERN_FONTS['arabic_heading'])
        production_title.pack(pady=20)
        
        # جدول الأنشطة الأخيرة
        activities_frame = tk.Frame(charts_container, bg=UIConfig.MODERN_COLORS['white'])
        activities_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        activities_title = tk.Label(activities_frame,
                                   text="الأنشطة الأخيرة",
                                   bg=UIConfig.MODERN_COLORS['white'],
                                   fg=UIConfig.MODERN_COLORS['dark'],
                                   font=UIConfig.MODERN_FONTS['arabic_heading'])
        activities_title.pack(pady=20)
        
        # قائمة الأنشطة
        self.create_activities_list(activities_frame)
        
    def create_activities_list(self, parent):
        """إنشاء قائمة الأنشطة الأخيرة"""
        activities_list = tk.Frame(parent, bg=UIConfig.MODERN_COLORS['white'])
        activities_list.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # أنشطة وهمية للعرض
        activities = [
            ("تم إنشاء أمر تشغيل جديد", "منذ 5 دقائق", UIConfig.MODERN_COLORS['info']),
            ("تم إكمال عملية تشغيل", "منذ 15 دقيقة", UIConfig.MODERN_COLORS['success']),
            ("تم صرف مواد خام", "منذ 30 دقيقة", UIConfig.MODERN_COLORS['warning']),
            ("تم رصد قطاع معيب", "منذ ساعة", UIConfig.MODERN_COLORS['danger']),
        ]
        
        for activity, time, color in activities:
            activity_frame = tk.Frame(activities_list, bg=UIConfig.MODERN_COLORS['gray_100'])
            activity_frame.pack(fill='x', pady=5)
            
            # نقطة ملونة
            dot = tk.Label(activity_frame, text="●", bg=UIConfig.MODERN_COLORS['gray_100'],
                          fg=color, font=('Segoe UI', 12))
            dot.pack(side='left', padx=(10, 5))
            
            # النشاط
            activity_label = tk.Label(activity_frame, text=activity,
                                     bg=UIConfig.MODERN_COLORS['gray_100'],
                                     fg=UIConfig.MODERN_COLORS['dark'],
                                     font=UIConfig.MODERN_FONTS['arabic_body'],
                                     anchor='w')
            activity_label.pack(side='left', fill='x', expand=True)
            
            # الوقت
            time_label = tk.Label(activity_frame, text=time,
                                 bg=UIConfig.MODERN_COLORS['gray_100'],
                                 fg=UIConfig.MODERN_COLORS['gray_600'],
                                 font=UIConfig.MODERN_FONTS['arabic_small'])
            time_label.pack(side='right', padx=10)
            
    def get_system_statistics(self):
        """جلب إحصائيات النظام"""
        stats = {}
        
        try:
            # عدد المنتجات
            products = self.db.get_all_records('products')
            stats['products'] = len(products) if products else 0
            
            # عدد أوامر التشغيل
            work_orders = self.db.get_all_records('work_orders')
            stats['work_orders'] = len(work_orders) if work_orders else 0
            
            # عدد العمليات المكتملة
            completed_orders = self.db.get_all_records('work_orders', "status = 'مكتمل'")
            stats['completed_operations'] = len(completed_orders) if completed_orders else 0
            
            # عدد القطاعات المعيبة
            defective_items = self.db.get_all_records('defective_items')
            stats['defective_items'] = len(defective_items) if defective_items else 0
            
        except Exception as e:
            print(f"خطأ في جلب الإحصائيات: {e}")
            
        return stats

    # دوال فتح النوافذ
    def open_products(self):
        """فتح نافذة المنتجات"""
        self.update_breadcrumb("البيانات الأساسية > المنتجات")
        self.update_status("فتح نافذة المنتجات...")
        ProductsWindow(self.root)

    def open_parts(self):
        """فتح نافذة الأجزاء"""
        self.update_breadcrumb("البيانات الأساسية > الأجزاء")
        self.update_status("فتح نافذة الأجزاء...")
        PartsWindow(self.root)

    def open_sectors(self):
        """فتح نافذة القطاعات"""
        self.update_breadcrumb("البيانات الأساسية > القطاعات")
        self.update_status("فتح نافذة القطاعات...")
        SectorsWindow(self.root)

    def open_workshops(self):
        """فتح نافذة الورش"""
        self.update_breadcrumb("البيانات الأساسية > الورش")
        self.update_status("فتح نافذة الورش...")
        WorkshopsWindow(self.root)

    def open_operations(self):
        """فتح نافذة العمليات"""
        self.update_breadcrumb("البيانات الأساسية > العمليات")
        self.update_status("فتح نافذة العمليات...")
        OperationsWindow(self.root)

    def open_part_operations(self):
        """فتح نافذة ربط الأجزاء بالعمليات"""
        self.update_breadcrumb("البيانات الأساسية > ربط الأجزاء بالعمليات")
        self.update_status("فتح نافذة ربط الأجزاء بالعمليات...")
        PartOperationsWindow(self.root)

    def open_product_parts(self):
        """فتح نافذة ربط المنتجات بالأجزاء"""
        self.update_breadcrumb("البيانات الأساسية > ربط المنتجات بالأجزاء")
        self.update_status("فتح نافذة ربط المنتجات بالأجزاء...")
        ProductPartsWindow(self.root)

    def open_work_orders(self):
        """فتح نافذة أوامر التشغيل"""
        self.update_breadcrumb("بيانات الحركة > أوامر التشغيل")
        self.update_status("فتح نافذة أوامر التشغيل...")
        WorkOrdersWindow(self.root)

    def open_operation_cards(self):
        """فتح نافذة بطاقات التشغيل"""
        self.update_breadcrumb("بيانات الحركة > بطاقات التشغيل")
        self.update_status("فتح نافذة بطاقات التشغيل...")
        OperationCardsWindow(self.root)

    def open_material_issues(self):
        """فتح نافذة صرف المواد"""
        self.update_breadcrumb("بيانات الحركة > صرف المواد")
        self.update_status("فتح نافذة صرف المواد...")
        MaterialIssuesWindow(self.root)

    def open_defective_items(self):
        """فتح نافذة القطاعات المعيبة"""
        self.update_breadcrumb("بيانات الحركة > القطاعات المعيبة")
        self.update_status("فتح نافذة القطاعات المعيبة...")
        DefectiveItemsWindow(self.root)

    def open_queries(self):
        """فتح نافذة الاستعلامات"""
        self.update_breadcrumb("التقارير والاستعلامات > الاستعلامات")
        self.update_status("فتح نافذة الاستعلامات...")
        QueriesWindow(self.root)

    def open_reports(self):
        """فتح نافذة التقارير"""
        self.update_breadcrumb("التقارير والاستعلامات > التقارير")
        self.update_status("فتح نافذة التقارير...")
        ReportsWindow(self.root)

    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        self.update_status("جاري إنشاء نسخة احتياطية...")
        try:
            success, message = self.db.create_backup()
            if success:
                messagebox.showinfo("نجح", "تم إنشاء النسخة الاحتياطية بنجاح")
                self.update_status("تم إنشاء النسخة الاحتياطية بنجاح")
            else:
                messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية: {message}")
                self.update_status("فشل في إنشاء النسخة الاحتياطية")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
            self.update_status("حدث خطأ أثناء إنشاء النسخة الاحتياطية")

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        self.update_breadcrumb("النظام > الإعدادات")
        self.clear_content_area()

        # إنشاء نافذة الإعدادات
        settings_frame = tk.Frame(self.content_area, bg=UIConfig.MODERN_COLORS['white'])
        settings_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان الإعدادات
        title_label = tk.Label(settings_frame,
                              text="إعدادات النظام",
                              bg=UIConfig.MODERN_COLORS['white'],
                              fg=UIConfig.MODERN_COLORS['dark'],
                              font=UIConfig.MODERN_FONTS['arabic_title'])
        title_label.pack(pady=20)

        # معلومات النظام
        info_frame = tk.LabelFrame(settings_frame, text="معلومات النظام",
                                  font=UIConfig.MODERN_FONTS['arabic_heading'])
        info_frame.pack(fill='x', padx=20, pady=10)

        system_info = SystemConfig.get_system_info()
        for key, value in system_info.items():
            info_row = tk.Frame(info_frame)
            info_row.pack(fill='x', padx=10, pady=5)

            tk.Label(info_row, text=f"{key}:", font=UIConfig.MODERN_FONTS['arabic_body']).pack(side='left')
            tk.Label(info_row, text=str(value), font=UIConfig.MODERN_FONTS['body']).pack(side='right')

    def show_about(self):
        """عرض نافذة حول النظام"""
        self.update_breadcrumb("النظام > حول النظام")

        about_window = tk.Toplevel(self.root)
        about_window.title("حول النظام")
        about_window.geometry("500x400")
        about_window.configure(bg=UIConfig.MODERN_COLORS['white'])
        about_window.resizable(False, False)

        # توسيط النافذة
        about_window.transient(self.root)
        about_window.grab_set()

        # محتوى النافذة
        content_frame = tk.Frame(about_window, bg=UIConfig.MODERN_COLORS['white'])
        content_frame.pack(fill='both', expand=True, padx=30, pady=30)

        # أيقونة النظام
        icon_label = tk.Label(content_frame,
                             text=UIConfig.ICONS['dashboard'],
                             bg=UIConfig.MODERN_COLORS['white'],
                             fg=UIConfig.MODERN_COLORS['primary'],
                             font=('Segoe UI', 48))
        icon_label.pack(pady=20)

        # اسم النظام
        name_label = tk.Label(content_frame,
                             text=SystemConfig.SYSTEM_NAME,
                             bg=UIConfig.MODERN_COLORS['white'],
                             fg=UIConfig.MODERN_COLORS['dark'],
                             font=UIConfig.MODERN_FONTS['arabic_title'])
        name_label.pack(pady=10)

        # الإصدار
        version_label = tk.Label(content_frame,
                                text=f"الإصدار {SystemConfig.VERSION}",
                                bg=UIConfig.MODERN_COLORS['white'],
                                fg=UIConfig.MODERN_COLORS['gray_600'],
                                font=UIConfig.MODERN_FONTS['arabic_body'])
        version_label.pack(pady=5)

        # المطور
        developer_label = tk.Label(content_frame,
                                  text=f"تطوير: {SystemConfig.DEVELOPER}",
                                  bg=UIConfig.MODERN_COLORS['white'],
                                  fg=UIConfig.MODERN_COLORS['gray_600'],
                                  font=UIConfig.MODERN_FONTS['arabic_body'])
        developer_label.pack(pady=5)

        # تاريخ البناء
        build_label = tk.Label(content_frame,
                              text=f"تاريخ البناء: {SystemConfig.BUILD_DATE}",
                              bg=UIConfig.MODERN_COLORS['white'],
                              fg=UIConfig.MODERN_COLORS['gray_600'],
                              font=UIConfig.MODERN_FONTS['arabic_body'])
        build_label.pack(pady=5)

        # وصف النظام
        description = tk.Text(content_frame,
                             height=6,
                             bg=UIConfig.MODERN_COLORS['gray_100'],
                             fg=UIConfig.MODERN_COLORS['dark'],
                             font=UIConfig.MODERN_FONTS['arabic_body'],
                             wrap='word',
                             border=0)
        description.pack(fill='x', pady=20)

        description_text = """
نظام شامل لإدارة التكاليف الصناعية مصمم خصيصاً للمصانع والشركات الصناعية.
يوفر النظام إدارة متكاملة للمنتجات والأجزاء والعمليات وأوامر التشغيل
مع تتبع دقيق للتكاليف والجودة.

يتضمن النظام وحدات للبيانات الأساسية وبيانات الحركة والتقارير
والاستعلامات مع واجهة مستخدم حديثة وسهلة الاستخدام.
        """
        description.insert('1.0', description_text.strip())
        description.config(state='disabled')

        # زر الإغلاق
        close_btn = tk.Button(content_frame,
                             text="إغلاق",
                             bg=UIConfig.MODERN_COLORS['primary'],
                             fg=UIConfig.MODERN_COLORS['white'],
                             font=UIConfig.MODERN_FONTS['arabic_body'],
                             border=0,
                             padx=30,
                             pady=10,
                             command=about_window.destroy)
        close_btn.pack(pady=20)

    def on_closing(self):
        """عند إغلاق النظام"""
        result = messagebox.askyesno("تأكيد الخروج", "هل تريد إغلاق النظام؟")
        if result:
            self.root.destroy()

    def run(self):
        """تشغيل النظام"""
        self.root.mainloop()

if __name__ == "__main__":
    app = ModernMainWindow()
    app.run()

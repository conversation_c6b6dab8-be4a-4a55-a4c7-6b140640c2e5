import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import os
from database import DatabaseManager
from products_window import ProductsWindow
from config import SystemConfig, UIConfig

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(SystemConfig.SYSTEM_NAME)
        self.root.geometry(f"{SystemConfig.WINDOW_WIDTH}x{SystemConfig.WINDOW_HEIGHT}")
        self.root.minsize(SystemConfig.WINDOW_MIN_WIDTH, SystemConfig.WINDOW_MIN_HEIGHT)
        self.root.configure(bg=SystemConfig.BACKGROUND_COLOR)

        # تعيين الخطوط من الإعدادات
        self.arabic_font = SystemConfig.ARABIC_FONT
        self.title_font = SystemConfig.TITLE_FONT
        
        # إنشاء قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغير السنة المالية الحالية
        self.current_fiscal_year = tk.StringVar()
        self.current_fiscal_year.set(SystemConfig.DEFAULT_FISCAL_YEAR)
        
        self.create_widgets()
        self.setup_layout()
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.root)
        
        # شريط العنوان
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text=SystemConfig.SYSTEM_NAME,
            font=self.title_font
        )
        
        # إطار اختيار السنة المالية
        self.year_frame = ttk.LabelFrame(self.main_frame, text="السنة المالية", padding=10)
        self.year_combo = ttk.Combobox(
            self.year_frame,
            textvariable=self.current_fiscal_year,
            values=SystemConfig.FISCAL_YEARS,
            state="readonly",
            font=self.arabic_font,
            width=15
        )
        
        # الإطار الأيسر للقوائم الرئيسية
        self.menu_frame = ttk.LabelFrame(self.main_frame, text="القوائم الرئيسية", padding=10)
        
        # أزرار القوائم الرئيسية
        self.basic_data_btn = ttk.Button(
            self.menu_frame,
            text="البيانات الأساسية",
            command=self.open_basic_data,
            width=20
        )
        
        self.movement_data_btn = ttk.Button(
            self.menu_frame,
            text="بيانات الحركة",
            command=self.open_movement_data,
            width=20
        )
        
        self.inquiry_btn = ttk.Button(
            self.menu_frame,
            text="الاستعلام",
            command=self.open_inquiry,
            width=20
        )
        
        self.reports_btn = ttk.Button(
            self.menu_frame,
            text="التقارير",
            command=self.open_reports,
            width=20
        )
        
        self.backup_btn = ttk.Button(
            self.menu_frame,
            text="نسخة احتياطية",
            command=self.open_backup,
            width=20
        )
        
        self.exit_btn = ttk.Button(
            self.menu_frame,
            text="الخروج من النظام",
            command=self.exit_application,
            width=20
        )
        
        # الإطار الأيمن لعرض المحتوى
        self.content_frame = ttk.LabelFrame(self.main_frame, text="المحتوى", padding=10)
        
        # رسالة ترحيب
        self.welcome_label = ttk.Label(
            self.content_frame,
            text="مرحباً بك في نظام التكاليف الصناعي المتكامل\n\nاختر من القائمة الجانبية للبدء",
            font=self.arabic_font,
            justify='center'
        )
        
        # شريط الحالة
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(
            self.status_frame,
            text=f"جاهز - السنة المالية: {self.current_fiscal_year.get()}",
            font=('Arial', 10)
        )
        
    def setup_layout(self):
        """ترتيب عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط العنوان
        self.title_frame.pack(fill='x', pady=(0, 10))
        self.title_label.pack()
        
        # إطار السنة المالية
        self.year_frame.pack(fill='x', pady=(0, 10))
        self.year_combo.pack()
        
        # إطار القوائم (يسار)
        self.menu_frame.pack(side='left', fill='y', padx=(0, 10))
        
        # ترتيب أزرار القوائم
        buttons = [
            self.basic_data_btn,
            self.movement_data_btn,
            self.inquiry_btn,
            self.reports_btn,
            self.backup_btn,
            self.exit_btn
        ]
        
        for i, btn in enumerate(buttons):
            btn.pack(pady=5, fill='x')
            if i == 4:  # إضافة مسافة قبل زر الخروج
                ttk.Separator(self.menu_frame, orient='horizontal').pack(fill='x', pady=10)
        
        # إطار المحتوى (يمين)
        self.content_frame.pack(side='right', fill='both', expand=True)
        self.welcome_label.pack(expand=True)
        
        # شريط الحالة
        self.status_frame.pack(fill='x', side='bottom', pady=(10, 0))
        self.status_label.pack(side='left')
        
        # ربط تغيير السنة المالية
        self.year_combo.bind('<<ComboboxSelected>>', self.on_year_changed)
    
    def on_year_changed(self, event=None):
        """عند تغيير السنة المالية"""
        year = self.current_fiscal_year.get()
        self.status_label.config(text=f"جاهز - السنة المالية: {year}")
        messagebox.showinfo("تغيير السنة المالية", f"تم تغيير السنة المالية إلى: {year}")
    
    def clear_content_frame(self):
        """مسح محتوى الإطار الأيمن"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def open_basic_data(self):
        """فتح نافذة البيانات الأساسية"""
        self.clear_content_frame()
        
        # إنشاء قائمة فرعية للبيانات الأساسية
        basic_frame = ttk.Frame(self.content_frame)
        basic_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        ttk.Label(basic_frame, text="البيانات الأساسية", font=self.title_font).pack(pady=(0, 20))
        
        # أزرار البيانات الأساسية
        buttons_data = [
            ("المنتجات", self.open_products),
            ("الأجزاء", self.open_parts),
            ("القطاعات", self.open_sectors),
            ("الورش", self.open_workshops),
            ("عمليات التشغيل", self.open_operations),
            ("علاقة الأجزاء بالعمليات", self.open_part_operations)
        ]
        
        for text, command in buttons_data:
            btn = ttk.Button(basic_frame, text=text, command=command, width=25)
            btn.pack(pady=5)
    
    def open_movement_data(self):
        """فتح نافذة بيانات الحركة"""
        self.clear_content_frame()
        
        movement_frame = ttk.Frame(self.content_frame)
        movement_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        ttk.Label(movement_frame, text="بيانات الحركة", font=self.title_font).pack(pady=(0, 20))
        
        buttons_data = [
            ("أوامر التشغيل", self.open_work_orders),
            ("بطاقة التشغيل", self.open_operation_cards),
            ("صرف الخامة", self.open_material_issues),
            ("القطاعات غير السليمة", self.open_defective_items),
            ("ترحيل تكلفة الوحدة", self.open_unit_cost_transfer)
        ]
        
        for text, command in buttons_data:
            btn = ttk.Button(movement_frame, text=text, command=command, width=25)
            btn.pack(pady=5)
    
    def open_inquiry(self):
        """فتح نافذة الاستعلام"""
        self.clear_content_frame()
        
        inquiry_frame = ttk.Frame(self.content_frame)
        inquiry_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        ttk.Label(inquiry_frame, text="الاستعلام", font=self.title_font).pack(pady=(0, 20))
        
        ttk.Label(inquiry_frame, text="قريباً...", font=self.arabic_font).pack()
    
    def open_reports(self):
        """فتح نافذة التقارير"""
        self.clear_content_frame()
        
        reports_frame = ttk.Frame(self.content_frame)
        reports_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        ttk.Label(reports_frame, text="التقارير", font=self.title_font).pack(pady=(0, 20))
        
        ttk.Label(reports_frame, text="قريباً...", font=self.arabic_font).pack()
    
    def open_backup(self):
        """فتح نافذة النسخ الاحتياطي"""
        self.clear_content_frame()
        
        backup_frame = ttk.Frame(self.content_frame)
        backup_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        ttk.Label(backup_frame, text="النسخ الاحتياطي", font=self.title_font).pack(pady=(0, 20))
        
        # زر إنشاء نسخة احتياطية
        backup_btn = ttk.Button(
            backup_frame,
            text="إنشاء نسخة احتياطية",
            command=self.create_backup,
            width=25
        )
        backup_btn.pack(pady=10)
        
        # زر استرجاع نسخة احتياطية
        restore_btn = ttk.Button(
            backup_frame,
            text="استرجاع نسخة احتياطية",
            command=self.restore_backup,
            width=25
        )
        restore_btn.pack(pady=10)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            success, message = self.db.backup_database()
            if success:
                messagebox.showinfo("نجح", message)
            else:
                messagebox.showerror("خطأ", message)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def restore_backup(self):
        """استرجاع نسخة احتياطية"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        
        if file_path:
            result = messagebox.askyesno(
                "تأكيد",
                "هل أنت متأكد من استرجاع النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية."
            )
            
            if result:
                try:
                    success, message = self.db.restore_database(file_path)
                    if success:
                        messagebox.showinfo("نجح", message)
                    else:
                        messagebox.showerror("خطأ", message)
                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def exit_application(self):
        """الخروج من التطبيق"""
        result = messagebox.askyesno("تأكيد الخروج", "هل تريد الخروج من النظام؟")
        if result:
            self.root.quit()
    
    # دوال البيانات الأساسية
    def open_products(self):
        """فتح نافذة إدارة المنتجات"""
        products_window = ProductsWindow(self.root)
    
    def open_parts(self):
        messagebox.showinfo("الأجزاء", "نافذة الأجزاء قيد التطوير")
    
    def open_sectors(self):
        messagebox.showinfo("القطاعات", "نافذة القطاعات قيد التطوير")
    
    def open_workshops(self):
        messagebox.showinfo("الورش", "نافذة الورش قيد التطوير")
    
    def open_operations(self):
        messagebox.showinfo("عمليات التشغيل", "نافذة عمليات التشغيل قيد التطوير")
    
    def open_part_operations(self):
        messagebox.showinfo("علاقة الأجزاء بالعمليات", "نافذة علاقة الأجزاء بالعمليات قيد التطوير")
    
    # دوال بيانات الحركة (ستتم إضافتها لاحقاً)
    def open_work_orders(self):
        messagebox.showinfo("أوامر التشغيل", "نافذة أوامر التشغيل قيد التطوير")
    
    def open_operation_cards(self):
        messagebox.showinfo("بطاقة التشغيل", "نافذة بطاقة التشغيل قيد التطوير")
    
    def open_material_issues(self):
        messagebox.showinfo("صرف الخامة", "نافذة صرف الخامة قيد التطوير")
    
    def open_defective_items(self):
        messagebox.showinfo("القطاعات غير السليمة", "نافذة القطاعات غير السليمة قيد التطوير")
    
    def open_unit_cost_transfer(self):
        messagebox.showinfo("ترحيل تكلفة الوحدة", "نافذة ترحيل تكلفة الوحدة قيد التطوير")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.run()

import tkinter as tk
from tkinter import ttk, messagebox
from database import DatabaseManager
from config import SystemConfig, UIConfig

class ProductPartsWindow:
    def __init__(self, parent=None):
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("علاقة المنتجات بالأجزاء (BOM)")
        self.window.geometry("1300x700")
        self.window.configure(bg=SystemConfig.BACKGROUND_COLOR)
        
        # الخطوط من الإعدادات
        self.arabic_font = SystemConfig.ARABIC_FONT
        self.title_font = SystemConfig.TITLE_FONT
        
        # قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغيرات النموذج
        self.selected_relation_id = None
        self.setup_variables()
        
        self.create_widgets()
        self.setup_layout()
        self.load_products_combo()
        self.load_parts_combo()
        self.load_product_parts()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.product_var = tk.StringVar()
        self.part_var = tk.StringVar()
        self.quantity_var = tk.DoubleVar()
        self.unit_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # تعيين القيم الافتراضية
        self.quantity_var.set(1.0)
        self.unit_var.set("قطعة")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.window)
        
        # شريط العنوان
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="علاقة المنتجات بالأجزاء (Bill of Materials)",
            font=self.title_font
        )
        
        # إطار الإدخال
        self.input_frame = ttk.LabelFrame(self.main_frame, text="ربط منتج بجزء", padding=10)
        
        # حقول الإدخال
        ttk.Label(self.input_frame, text="المنتج:", font=self.arabic_font).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.product_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.product_var,
            font=self.arabic_font,
            width=40,
            state="readonly"
        )
        self.product_combo.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الجزء:", font=self.arabic_font).grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.part_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.part_var,
            font=self.arabic_font,
            width=40,
            state="readonly"
        )
        self.part_combo.grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الكمية المطلوبة:", font=self.arabic_font).grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.quantity_entry = ttk.Entry(self.input_frame, textvariable=self.quantity_var, font=self.arabic_font, width=40)
        self.quantity_entry.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الوحدة:", font=self.arabic_font).grid(row=1, column=2, sticky='e', padx=5, pady=5)
        self.unit_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.unit_var,
            values=SystemConfig.UNITS,
            font=self.arabic_font,
            width=37
        )
        self.unit_combo.grid(row=1, column=3, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="ملاحظات:", font=self.arabic_font).grid(row=2, column=0, sticky='e', padx=5, pady=5)
        self.notes_entry = ttk.Entry(self.input_frame, textvariable=self.notes_var, font=self.arabic_font, width=80)
        self.notes_entry.grid(row=2, column=1, columnspan=3, padx=5, pady=5, sticky='ew')
        
        # إطار الأزرار
        self.buttons_frame = ttk.Frame(self.input_frame)
        self.buttons_frame.grid(row=3, column=0, columnspan=4, pady=20)
        
        self.add_btn = ttk.Button(
            self.buttons_frame,
            text="إضافة",
            command=self.add_product_part,
            width=12
        )
        self.add_btn.pack(side='left', padx=5)
        
        self.update_btn = ttk.Button(
            self.buttons_frame,
            text="تحديث",
            command=self.update_product_part,
            width=12,
            state='disabled'
        )
        self.update_btn.pack(side='left', padx=5)
        
        self.delete_btn = ttk.Button(
            self.buttons_frame,
            text="حذف",
            command=self.delete_product_part,
            width=12,
            state='disabled'
        )
        self.delete_btn.pack(side='left', padx=5)
        
        self.clear_btn = ttk.Button(
            self.buttons_frame,
            text="مسح",
            command=self.clear_form,
            width=12
        )
        self.clear_btn.pack(side='left', padx=5)
        
        self.calculate_cost_btn = ttk.Button(
            self.buttons_frame,
            text="حساب تكلفة المنتج",
            command=self.calculate_product_cost,
            width=15
        )
        self.calculate_cost_btn.pack(side='left', padx=5)
        
        # إطار قائمة العلاقات
        self.list_frame = ttk.LabelFrame(self.main_frame, text="قائمة علاقات المنتجات بالأجزاء", padding=10)
        
        # جدول العلاقات
        columns = ('ID', 'المنتج', 'الجزء', 'الكمية المطلوبة', 'الوحدة', 'تكلفة الجزء', 'إجمالي التكلفة', 'ملاحظات', 'تاريخ الإنشاء')
        self.relations_tree = ttk.Treeview(self.list_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.relations_tree.heading(col, text=col)
            if col == 'ID':
                self.relations_tree.column(col, width=50)
            elif col in ['المنتج', 'الجزء']:
                self.relations_tree.column(col, width=150)
            elif col in ['الكمية المطلوبة', 'الوحدة']:
                self.relations_tree.column(col, width=100)
            elif col in ['تكلفة الجزء', 'إجمالي التكلفة']:
                self.relations_tree.column(col, width=120)
            elif col == 'ملاحظات':
                self.relations_tree.column(col, width=200)
            else:
                self.relations_tree.column(col, width=120)
        
        # شريط التمرير
        self.scrollbar = ttk.Scrollbar(self.list_frame, orient='vertical', command=self.relations_tree.yview)
        self.relations_tree.configure(yscrollcommand=self.scrollbar.set)
        
        # ربط الأحداث
        self.relations_tree.bind('<<TreeviewSelect>>', self.on_relation_select)
        
        # إطار البحث
        self.search_frame = ttk.LabelFrame(self.main_frame, text="البحث والتصفية", padding=10)
        
        ttk.Label(self.search_frame, text="البحث:", font=self.arabic_font).pack(side='left', padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, font=self.arabic_font, width=30)
        self.search_entry.pack(side='left', padx=5)
        
        ttk.Label(self.search_frame, text="المنتج:", font=self.arabic_font).pack(side='left', padx=5)
        self.filter_product_var = tk.StringVar()
        self.filter_product_combo = ttk.Combobox(
            self.search_frame,
            textvariable=self.filter_product_var,
            font=self.arabic_font,
            width=25,
            state="readonly"
        )
        self.filter_product_combo.pack(side='left', padx=5)
        
        self.search_btn = ttk.Button(
            self.search_frame,
            text="بحث",
            command=self.search_product_parts,
            width=10
        )
        self.search_btn.pack(side='left', padx=5)
        
        self.refresh_btn = ttk.Button(
            self.search_frame,
            text="تحديث",
            command=self.load_product_parts,
            width=10
        )
        self.refresh_btn.pack(side='left', padx=5)
        
        # ربط البحث بالكتابة
        self.search_var.trace('w', self.on_search_change)
        self.filter_product_var.trace('w', self.on_filter_change)
        
    def setup_layout(self):
        """ترتيب عناصر الواجهة"""
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.title_frame.pack(fill='x', pady=(0, 10))
        self.title_label.pack()
        
        self.input_frame.pack(fill='x', pady=(0, 10))
        
        self.search_frame.pack(fill='x', pady=(0, 10))
        
        self.list_frame.pack(fill='both', expand=True)
        self.relations_tree.pack(side='left', fill='both', expand=True)
        self.scrollbar.pack(side='right', fill='y')
        
    def load_products_combo(self):
        """تحميل قائمة المنتجات في الـ combobox"""
        products = self.db.get_all_records('products', 'active = 1 ORDER BY name')
        product_values = [f"{product['id']} - {product['name']}" for product in products]
        self.product_combo['values'] = product_values
        
        # تحميل قائمة المنتجات للفلتر
        filter_values = ["الكل"] + product_values
        self.filter_product_combo['values'] = filter_values
        self.filter_product_combo.set("الكل")
        
    def load_parts_combo(self):
        """تحميل قائمة الأجزاء في الـ combobox"""
        parts = self.db.get_all_records('parts', 'active = 1 ORDER BY name')
        part_values = [f"{part['id']} - {part['name']}" for part in parts]
        self.part_combo['values'] = part_values
        
    def clear_form(self):
        """مسح النموذج"""
        self.product_var.set("")
        self.part_var.set("")
        self.quantity_var.set(1.0)
        self.unit_var.set("قطعة")
        self.notes_var.set("")
        self.selected_relation_id = None
        
        self.add_btn.config(state='normal')
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
        
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.product_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار المنتج")
            return False
        
        if not self.part_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار الجزء")
            return False
        
        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                return False
        except:
            messagebox.showerror("خطأ", "الكمية يجب أن تكون رقم صحيح")
            return False
        
        return True
        
    def get_id_from_combo(self, combo_text):
        """استخراج المعرف من النص المختار"""
        if combo_text:
            return int(combo_text.split(' - ')[0])
        return None
        
    def add_product_part(self):
        """إضافة علاقة جديدة"""
        if not self.validate_form():
            return
        
        product_id = self.get_id_from_combo(self.product_var.get())
        part_id = self.get_id_from_combo(self.part_var.get())
        
        if not product_id or not part_id:
            messagebox.showerror("خطأ", "يجب اختيار المنتج والجزء")
            return
        
        # التحقق من عدم وجود نفس العلاقة
        existing = self.db.get_all_records('product_parts', 
                                         'product_id = ? AND part_id = ?', 
                                         [product_id, part_id])
        if existing:
            messagebox.showerror("خطأ", "هذه العلاقة موجودة بالفعل")
            return
        
        data = {
            'product_id': product_id,
            'part_id': part_id,
            'quantity': self.quantity_var.get(),
            'unit': self.unit_var.get(),
            'notes': self.notes_var.get().strip()
        }
        
        try:
            success = self.db.insert_record('product_parts', data)
            if success:
                messagebox.showinfo("نجح", "تم إضافة العلاقة بنجاح")
                self.clear_form()
                self.load_product_parts()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة العلاقة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def update_product_part(self):
        """تحديث علاقة موجودة"""
        if not self.selected_relation_id:
            return
        
        if not self.validate_form():
            return
        
        product_id = self.get_id_from_combo(self.product_var.get())
        part_id = self.get_id_from_combo(self.part_var.get())
        
        if not product_id or not part_id:
            messagebox.showerror("خطأ", "يجب اختيار المنتج والجزء")
            return
        
        data = {
            'product_id': product_id,
            'part_id': part_id,
            'quantity': self.quantity_var.get(),
            'unit': self.unit_var.get(),
            'notes': self.notes_var.get().strip()
        }
        
        try:
            success = self.db.update_record('product_parts', data, 'id = ?', [self.selected_relation_id])
            if success:
                messagebox.showinfo("نجح", "تم تحديث العلاقة بنجاح")
                self.clear_form()
                self.load_product_parts()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث العلاقة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def delete_product_part(self):
        """حذف علاقة"""
        if not self.selected_relation_id:
            return
        
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه العلاقة؟")
        if result:
            try:
                success = self.db.delete_record('product_parts', 'id = ?', [self.selected_relation_id])
                if success:
                    messagebox.showinfo("نجح", "تم حذف العلاقة بنجاح")
                    self.clear_form()
                    self.load_product_parts()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف العلاقة")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def load_product_parts(self):
        """تحميل قائمة العلاقات"""
        # مسح البيانات الحالية
        for item in self.relations_tree.get_children():
            self.relations_tree.delete(item)
        
        # جلب البيانات من قاعدة البيانات مع ربط الجداول
        query = """
        SELECT pp.*, pr.name as product_name, pt.name as part_name, pt.standard_cost 
        FROM product_parts pp 
        LEFT JOIN products pr ON pp.product_id = pr.id 
        LEFT JOIN parts pt ON pp.part_id = pt.id 
        ORDER BY pr.name, pt.name
        """
        success, relations = self.db.execute_query(query)
        
        if success:
            for relation in relations:
                created_date = relation['created_date'][:10] if relation['created_date'] else ""
                part_cost = relation['standard_cost'] or 0
                total_cost = relation['quantity'] * part_cost
                
                self.relations_tree.insert('', 'end', values=(
                    relation['id'],
                    relation['product_name'] or "",
                    relation['part_name'] or "",
                    f"{relation['quantity']:.2f}",
                    relation['unit'] or "",
                    f"{part_cost:.2f}",
                    f"{total_cost:.2f}",
                    relation['notes'] or "",
                    created_date
                ))
    
    def on_relation_select(self, event):
        """عند اختيار علاقة من القائمة"""
        selection = self.relations_tree.selection()
        if selection:
            item = self.relations_tree.item(selection[0])
            values = item['values']
            
            self.selected_relation_id = values[0]
            
            # البحث عن المنتج المناسب في الـ combobox
            product_name = values[1]
            for product_option in self.product_combo['values']:
                if product_name in product_option:
                    self.product_var.set(product_option)
                    break
            
            # البحث عن الجزء المناسب في الـ combobox
            part_name = values[2]
            for part_option in self.part_combo['values']:
                if part_name in part_option:
                    self.part_var.set(part_option)
                    break
            
            self.quantity_var.set(float(values[3]))
            self.unit_var.set(values[4])
            self.notes_var.set(values[7])
            
            self.add_btn.config(state='disabled')
            self.update_btn.config(state='normal')
            self.delete_btn.config(state='normal')
    
    def search_product_parts(self):
        """البحث في العلاقات"""
        search_term = self.search_var.get().strip()
        product_filter = self.filter_product_var.get()
        
        # مسح البيانات الحالية
        for item in self.relations_tree.get_children():
            self.relations_tree.delete(item)
        
        # بناء الاستعلام
        query = """
        SELECT pp.*, pr.name as product_name, pt.name as part_name, pt.standard_cost 
        FROM product_parts pp 
        LEFT JOIN products pr ON pp.product_id = pr.id 
        LEFT JOIN parts pt ON pp.part_id = pt.id 
        WHERE 1=1
        """
        params = []
        
        if search_term:
            query += " AND (pr.name LIKE ? OR pt.name LIKE ? OR pp.notes LIKE ?)"
            params.extend([f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"])
        
        if product_filter and product_filter != "الكل":
            product_id = self.get_id_from_combo(product_filter)
            if product_id:
                query += " AND pp.product_id = ?"
                params.append(product_id)
        
        query += " ORDER BY pr.name, pt.name"
        
        success, relations = self.db.execute_query(query, params)
        
        if success:
            for relation in relations:
                created_date = relation['created_date'][:10] if relation['created_date'] else ""
                part_cost = relation['standard_cost'] or 0
                total_cost = relation['quantity'] * part_cost
                
                self.relations_tree.insert('', 'end', values=(
                    relation['id'],
                    relation['product_name'] or "",
                    relation['part_name'] or "",
                    f"{relation['quantity']:.2f}",
                    relation['unit'] or "",
                    f"{part_cost:.2f}",
                    f"{total_cost:.2f}",
                    relation['notes'] or "",
                    created_date
                ))
    
    def calculate_product_cost(self):
        """حساب تكلفة المنتج بناءً على أجزائه"""
        product_filter = self.filter_product_var.get()
        if not product_filter or product_filter == "الكل":
            messagebox.showwarning("تحذير", "يرجى اختيار منتج محدد من قائمة التصفية")
            return
        
        product_id = self.get_id_from_combo(product_filter)
        if not product_id:
            return
        
        # حساب إجمالي تكلفة أجزاء المنتج
        query = """
        SELECT pp.quantity, pt.standard_cost, pt.name as part_name
        FROM product_parts pp 
        LEFT JOIN parts pt ON pp.part_id = pt.id 
        WHERE pp.product_id = ?
        """
        success, parts = self.db.execute_query(query, [product_id])
        
        if success:
            total_cost = 0
            cost_breakdown = []
            
            for part in parts:
                part_cost = (part['standard_cost'] or 0) * part['quantity']
                total_cost += part_cost
                cost_breakdown.append(f"• {part['part_name']}: {part['quantity']:.2f} × {part['standard_cost']:.2f} = {part_cost:.2f}")
            
            # عرض النتيجة
            product_name = product_filter.split(' - ')[1]
            result_text = f"تكلفة المنتج: {product_name}\n"
            result_text += "=" * 50 + "\n\n"
            result_text += "تفصيل التكلفة:\n"
            result_text += "\n".join(cost_breakdown)
            result_text += f"\n\nإجمالي التكلفة: {total_cost:.2f}"
            
            # إنشاء نافذة النتيجة
            result_window = tk.Toplevel(self.window)
            result_window.title("تكلفة المنتج")
            result_window.geometry("500x400")
            
            text_widget = tk.Text(result_window, wrap='word', font=self.arabic_font)
            text_widget.pack(fill='both', expand=True, padx=10, pady=10)
            text_widget.insert('1.0', result_text)
            text_widget.config(state='disabled')
        else:
            messagebox.showerror("خطأ", "فشل في حساب تكلفة المنتج")
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        self.window.after(500, self.search_product_parts)
    
    def on_filter_change(self, *args):
        """عند تغيير فلتر المنتج"""
        self.search_product_parts()

if __name__ == "__main__":
    app = ProductPartsWindow()
    app.window.mainloop()

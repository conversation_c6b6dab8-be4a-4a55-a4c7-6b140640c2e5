# 🏭 دليل تشغيل النظام المحدث

## 🚀 طرق التشغيل المختلفة

### ⭐ الطريقة الأولى: التشغيل السريع (موصى به)
```bash
python START.py
```
**المميزات:**
- تشغيل مباشر للنسخة المحدثة
- تحميل تلقائي للواجهة المودرن
- في حالة الفشل، يتم تحميل الواجهة الكلاسيكية تلقائياً

---

### 🎨 الطريقة الثانية: النسخة المحدثة مباشرة
```bash
python run_updated.py
```
**المميزات:**
- تشغيل النسخة المحدثة بالواجهة المودرن
- عرض تفصيلي للمميزات الجديدة
- رسائل واضحة في حالة وجود مشاكل

---

### 🎯 الطريقة الثالثة: نافذة الاختيار
```bash
python start_system.py
```
**المميزات:**
- نافذة اختيار بين الواجهة المودرن والكلاسيكية
- واجهة رسومية لاختيار نوع التشغيل
- معلومات مفصلة عن كل واجهة

---

### 🔄 الطريقة الرابعة: الواجهة المودرن فقط
```bash
python run_modern.py
```

### 📋 الطريقة الخامسة: الواجهة الكلاسيكية فقط
```bash
python run_classic.py
```

---

## ✨ المميزات الجديدة في النسخة المحدثة

### 🔗 ربط المنتجات بالأجزاء (BOM)
- **الملف**: `product_parts_window.py`
- **الوصول**: البيانات الأساسية > علاقة المنتجات بالأجزاء
- **الوظائف**:
  - ربط كل منتج بالأجزاء المطلوبة
  - تحديد الكمية المطلوبة من كل جزء
  - حساب تكلفة المنتج تلقائياً
  - وحدات قياس متنوعة
  - بحث وتصفية متقدم

### 🎨 الواجهة المودرن
- **الملف**: `modern_main_window.py`
- **المميزات**:
  - تصميم عصري ومودرن
  - ألوان متناسقة ومريحة للعين
  - شريط جانبي للتنقل مع إمكانية الطي
  - لوحة معلومات تفاعلية
  - إحصائيات فورية
  - تأثيرات بصرية احترافية
  - أيقونات حديثة
  - شريط علوي مع الوقت
  - مسار التنقل (Breadcrumb)

---

## 🔧 متطلبات التشغيل

### الملفات الأساسية المطلوبة:
- ✅ `database.py` - إدارة قاعدة البيانات
- ✅ `config.py` - إعدادات النظام
- ✅ `main_window.py` - الواجهة الكلاسيكية
- ✅ `modern_main_window.py` - الواجهة المودرن
- ✅ `modern_styles.py` - أنماط الواجهة المودرن
- ✅ `product_parts_window.py` - نافذة ربط المنتجات بالأجزاء

### نوافذ البيانات الأساسية:
- ✅ `products_window.py` - المنتجات
- ✅ `parts_window.py` - الأجزاء
- ✅ `sectors_window.py` - القطاعات
- ✅ `workshops_window.py` - الورش
- ✅ `operations_window.py` - العمليات
- ✅ `part_operations_window.py` - ربط الأجزاء بالعمليات

### نوافذ بيانات الحركة:
- ✅ `work_orders_window.py` - أوامر التشغيل
- ✅ `operation_cards_window.py` - بطاقات التشغيل
- ✅ `material_issues_window.py` - صرف المواد
- ✅ `defective_items_window.py` - القطاعات المعيبة

### نوافذ التقارير:
- ✅ `queries_window.py` - الاستعلامات
- ✅ `reports_window.py` - التقارير

---

## 🆘 حل المشاكل الشائعة

### ❌ خطأ: "ModuleNotFoundError"
**الحل:**
1. تأكد من وجود جميع الملفات في نفس المجلد
2. تأكد من تثبيت Python بشكل صحيح
3. جرب تشغيل: `python START.py`

### ❌ خطأ: "فشل في تهيئة قاعدة البيانات"
**الحل:**
1. تأكد من وجود ملف `database.py`
2. تأكد من صلاحيات الكتابة في المجلد
3. احذف ملف `industrial_costs.db` إن وجد وأعد التشغيل

### ❌ خطأ: "فشل في تحميل الواجهة المودرن"
**الحل:**
1. تأكد من وجود ملفات:
   - `modern_main_window.py`
   - `modern_styles.py`
2. جرب تشغيل الواجهة الكلاسيكية: `python run_classic.py`

---

## 📞 الدعم الفني

في حالة وجود أي مشاكل:
1. تأكد من وجود جميع الملفات المطلوبة
2. جرب طرق التشغيل المختلفة
3. راجع رسائل الخطأ في نافذة الأوامر
4. تواصل مع فريق الدعم الفني

---

## 🎯 نصائح للاستخدام الأمثل

1. **استخدم الواجهة المودرن** للحصول على أفضل تجربة
2. **ابدأ بإدخال البيانات الأساسية** قبل بيانات الحركة
3. **استخدم نظام BOM** لربط المنتجات بالأجزاء
4. **راجع لوحة المعلومات** للحصول على إحصائيات سريعة
5. **استخدم البحث والتصفية** للعثور على البيانات بسرعة

---

**🏭 نظام التكاليف الصناعي المتكامل - النسخة المحدثة 2.0**

@echo off
chcp 65001 > nul
title نظام التكاليف الصناعي المتكامل

echo.
echo ========================================
echo    نظام التكاليف الصناعي المتكامل
echo    Industrial Cost Management System
echo ========================================
echo.

REM فحص وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.6 أو أحدث من الموقع الرسمي:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo تم العثور على Python...
python --version

REM فحص وجود الملفات المطلوبة
if not exist "run_system.py" (
    echo خطأ: ملف run_system.py غير موجود
    echo تأكد من وجود جميع ملفات النظام في نفس المجلد
    echo.
    pause
    exit /b 1
)

if not exist "main_window.py" (
    echo خطأ: ملف main_window.py غير موجود
    echo تأكد من وجود جميع ملفات النظام في نفس المجلد
    echo.
    pause
    exit /b 1
)

if not exist "database.py" (
    echo خطأ: ملف database.py غير موجود
    echo تأكد من وجود جميع ملفات النظام في نفس المجلد
    echo.
    pause
    exit /b 1
)

echo تم العثور على جميع الملفات المطلوبة...
echo.

REM إنشاء المجلدات المطلوبة إذا لم تكن موجودة
if not exist "backups" mkdir backups
if not exist "reports" mkdir reports
if not exist "exports" mkdir exports
if not exist "logs" mkdir logs

echo جاري تشغيل النظام...
echo.

REM تشغيل النظام
python run_system.py

REM فحص حالة الخروج
if %errorlevel% neq 0 (
    echo.
    echo حدث خطأ أثناء تشغيل النظام
    echo كود الخطأ: %errorlevel%
    echo.
    echo للحصول على مساعدة، شغل الأمر التالي:
    echo python run_system.py --help
    echo.
    pause
) else (
    echo.
    echo تم إغلاق النظام بنجاح
)

pause

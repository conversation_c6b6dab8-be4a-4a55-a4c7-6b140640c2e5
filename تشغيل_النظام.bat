@echo off
chcp 65001 > nul
title Industrial Cost Management System

echo.
echo ========================================
echo    Industrial Cost Management System
echo ========================================
echo.

REM Check Python installation
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed
    echo Please install Python 3.6 or newer from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo Python found...
python --version

REM Check required files
if not exist "run_system.py" (
    echo Error: run_system.py not found
    echo Make sure all system files are in the same folder
    echo.
    pause
    exit /b 1
)

if not exist "main_window.py" (
    echo Error: main_window.py not found
    echo Make sure all system files are in the same folder
    echo.
    pause
    exit /b 1
)

if not exist "database.py" (
    echo Error: database.py not found
    echo Make sure all system files are in the same folder
    echo.
    pause
    exit /b 1
)

echo All required files found...
echo.

REM Create required folders if they don't exist
if not exist "backups" mkdir backups
if not exist "reports" mkdir reports
if not exist "exports" mkdir exports
if not exist "logs" mkdir logs

echo Starting the system...
echo.

REM Run the system
python run_system.py

REM Check exit status
if %errorlevel% neq 0 (
    echo.
    echo An error occurred while running the system
    echo Error code: %errorlevel%
    echo.
    echo For help, run the following command:
    echo python run_system.py --help
    echo.
    pause
) else (
    echo.
    echo System closed successfully
)

pause

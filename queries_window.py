import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from database import DatabaseManager
from config import SystemConfig, UIConfig

class QueriesWindow:
    def __init__(self, parent=None):
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("الاستعلامات")
        self.window.geometry("1300x700")
        self.window.configure(bg=SystemConfig.BACKGROUND_COLOR)
        
        # الخطوط من الإعدادات
        self.arabic_font = SystemConfig.ARABIC_FONT
        self.title_font = SystemConfig.TITLE_FONT
        
        # قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغيرات النموذج
        self.setup_variables()
        
        self.create_widgets()
        self.setup_layout()
        self.load_predefined_queries()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.query_type_var = tk.StringVar()
        self.from_date_var = tk.StringVar()
        self.to_date_var = tk.StringVar()
        self.product_filter_var = tk.StringVar()
        self.status_filter_var = tk.StringVar()
        
        # تعيين القيم الافتراضية
        today = date.today()
        self.from_date_var.set(today.replace(day=1).strftime("%Y-%m-%d"))  # بداية الشهر
        self.to_date_var.set(today.strftime("%Y-%m-%d"))  # اليوم
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.window)
        
        # شريط العنوان
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="الاستعلامات",
            font=self.title_font
        )
        
        # إطار الاستعلامات المحددة مسبقاً
        self.predefined_frame = ttk.LabelFrame(self.main_frame, text="الاستعلامات المحددة مسبقاً", padding=10)
        
        # قائمة الاستعلامات المحددة مسبقاً
        self.queries_list = tk.Listbox(
            self.predefined_frame,
            font=self.arabic_font,
            height=8,
            width=50
        )
        
        self.execute_predefined_btn = ttk.Button(
            self.predefined_frame,
            text="تنفيذ الاستعلام",
            command=self.execute_predefined_query,
            width=15
        )
        
        # إطار المرشحات
        self.filters_frame = ttk.LabelFrame(self.main_frame, text="المرشحات", padding=10)
        
        # مرشحات التاريخ
        ttk.Label(self.filters_frame, text="من تاريخ:", font=self.arabic_font).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.from_date_entry = ttk.Entry(self.filters_frame, textvariable=self.from_date_var, font=self.arabic_font, width=15)
        self.from_date_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.filters_frame, text="إلى تاريخ:", font=self.arabic_font).grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.to_date_entry = ttk.Entry(self.filters_frame, textvariable=self.to_date_var, font=self.arabic_font, width=15)
        self.to_date_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # مرشح المنتج
        ttk.Label(self.filters_frame, text="المنتج:", font=self.arabic_font).grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.product_filter_combo = ttk.Combobox(
            self.filters_frame,
            textvariable=self.product_filter_var,
            font=self.arabic_font,
            width=25,
            state="readonly"
        )
        self.product_filter_combo.grid(row=1, column=1, columnspan=2, padx=5, pady=5)
        
        # مرشح الحالة
        ttk.Label(self.filters_frame, text="الحالة:", font=self.arabic_font).grid(row=1, column=3, sticky='e', padx=5, pady=5)
        self.status_filter_combo = ttk.Combobox(
            self.filters_frame,
            textvariable=self.status_filter_var,
            values=["الكل"] + SystemConfig.WORK_ORDER_STATUSES,
            font=self.arabic_font,
            width=15,
            state="readonly"
        )
        self.status_filter_combo.set("الكل")
        self.status_filter_combo.grid(row=1, column=4, padx=5, pady=5)
        
        # إطار النتائج
        self.results_frame = ttk.LabelFrame(self.main_frame, text="نتائج الاستعلام", padding=10)
        
        # جدول النتائج
        self.results_tree = ttk.Treeview(self.results_frame, show='headings', height=15)
        
        # شريط التمرير للنتائج
        self.results_scrollbar_v = ttk.Scrollbar(self.results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=self.results_scrollbar_v.set)
        
        self.results_scrollbar_h = ttk.Scrollbar(self.results_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(xscrollcommand=self.results_scrollbar_h.set)
        
        # إطار الأزرار
        self.buttons_frame = ttk.Frame(self.main_frame)
        
        self.export_btn = ttk.Button(
            self.buttons_frame,
            text="تصدير النتائج",
            command=self.export_results,
            width=15
        )
        
        self.clear_btn = ttk.Button(
            self.buttons_frame,
            text="مسح النتائج",
            command=self.clear_results,
            width=15
        )
        
        self.refresh_btn = ttk.Button(
            self.buttons_frame,
            text="تحديث المرشحات",
            command=self.refresh_filters,
            width=15
        )
        
    def setup_layout(self):
        """ترتيب عناصر الواجهة"""
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.title_frame.pack(fill='x', pady=(0, 10))
        self.title_label.pack()
        
        # ترتيب الإطارات العلوية
        top_frame = ttk.Frame(self.main_frame)
        top_frame.pack(fill='x', pady=(0, 10))
        
        self.predefined_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))
        self.queries_list.pack(fill='both', expand=True, pady=(0, 10))
        self.execute_predefined_btn.pack()
        
        self.filters_frame.pack(side='right', fill='x', padx=(5, 0))
        
        # إطار النتائج
        self.results_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # ترتيب جدول النتائج
        results_grid_frame = ttk.Frame(self.results_frame)
        results_grid_frame.pack(fill='both', expand=True)
        
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        self.results_scrollbar_v.grid(row=0, column=1, sticky='ns')
        self.results_scrollbar_h.grid(row=1, column=0, sticky='ew')
        
        results_grid_frame.grid_rowconfigure(0, weight=1)
        results_grid_frame.grid_columnconfigure(0, weight=1)
        
        # إطار الأزرار
        self.buttons_frame.pack(fill='x')
        self.export_btn.pack(side='left', padx=5)
        self.clear_btn.pack(side='left', padx=5)
        self.refresh_btn.pack(side='left', padx=5)
        
    def load_predefined_queries(self):
        """تحميل الاستعلامات المحددة مسبقاً"""
        queries = [
            "تقرير أوامر التشغيل حسب الفترة",
            "تقرير التكاليف الفعلية مقابل المعيارية",
            "تقرير استهلاك المواد الخام",
            "تقرير القطاعات المعيبة",
            "تقرير كفاءة العمليات",
            "تقرير تكلفة المنتجات",
            "تقرير أداء الورش",
            "تقرير المخزون والاستهلاك",
            "تقرير الانحرافات في التكلفة",
            "تقرير ملخص الإنتاج"
        ]
        
        for query in queries:
            self.queries_list.insert(tk.END, query)
        
        # تحميل قائمة المنتجات للمرشح
        self.refresh_filters()
        
    def refresh_filters(self):
        """تحديث قوائم المرشحات"""
        # تحميل قائمة المنتجات
        products = self.db.get_all_records('products', 'active = 1 ORDER BY name')
        product_values = ["الكل"] + [f"{product['id']} - {product['name']}" for product in products]
        self.product_filter_combo['values'] = product_values
        self.product_filter_combo.set("الكل")
        
    def execute_predefined_query(self):
        """تنفيذ الاستعلام المحدد مسبقاً"""
        selection = self.queries_list.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار استعلام من القائمة")
            return
        
        query_name = self.queries_list.get(selection[0])
        
        # تنفيذ الاستعلام المناسب
        if query_name == "تقرير أوامر التشغيل حسب الفترة":
            self.work_orders_by_period()
        elif query_name == "تقرير التكاليف الفعلية مقابل المعيارية":
            self.actual_vs_standard_costs()
        elif query_name == "تقرير استهلاك المواد الخام":
            self.material_consumption_report()
        elif query_name == "تقرير القطاعات المعيبة":
            self.defective_items_report()
        elif query_name == "تقرير كفاءة العمليات":
            self.operations_efficiency_report()
        elif query_name == "تقرير تكلفة المنتجات":
            self.product_cost_report()
        elif query_name == "تقرير أداء الورش":
            self.workshop_performance_report()
        elif query_name == "تقرير المخزون والاستهلاك":
            self.inventory_consumption_report()
        elif query_name == "تقرير الانحرافات في التكلفة":
            self.cost_variance_report()
        elif query_name == "تقرير ملخص الإنتاج":
            self.production_summary_report()
        else:
            messagebox.showinfo("معلومات", f"الاستعلام '{query_name}' قيد التطوير")
    
    def work_orders_by_period(self):
        """تقرير أوامر التشغيل حسب الفترة"""
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        
        query = """
        SELECT wo.order_number, p.name as product_name, wo.quantity, 
               wo.start_date, wo.end_date, wo.status,
               COALESCE(SUM(oc.actual_cost), 0) as total_actual_cost,
               COALESCE(SUM(mi.total_cost), 0) as total_material_cost
        FROM work_orders wo
        LEFT JOIN products p ON wo.product_id = p.id
        LEFT JOIN operation_cards oc ON wo.id = oc.work_order_id
        LEFT JOIN material_issues mi ON wo.id = mi.work_order_id
        WHERE wo.start_date BETWEEN ? AND ?
        GROUP BY wo.id, wo.order_number, p.name, wo.quantity, wo.start_date, wo.end_date, wo.status
        ORDER BY wo.start_date DESC
        """
        
        success, results = self.db.execute_query(query, [from_date, to_date])
        
        if success:
            columns = ['رقم الأمر', 'المنتج', 'الكمية', 'تاريخ البداية', 'تاريخ الانتهاء', 'الحالة', 'تكلفة العمليات', 'تكلفة المواد']
            self.display_results(columns, results)
        else:
            messagebox.showerror("خطأ", "فشل في تنفيذ الاستعلام")
    
    def actual_vs_standard_costs(self):
        """تقرير التكاليف الفعلية مقابل المعيارية"""
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        
        query = """
        SELECT p.name as part_name, o.name as operation_name,
               po.standard_time, AVG(oc.actual_time) as avg_actual_time,
               o.cost_per_hour,
               (po.standard_time * o.cost_per_hour) as standard_cost,
               AVG(oc.actual_cost) as avg_actual_cost,
               (AVG(oc.actual_cost) - (po.standard_time * o.cost_per_hour)) as variance
        FROM part_operations po
        JOIN parts p ON po.part_id = p.id
        JOIN operations o ON po.operation_id = o.id
        LEFT JOIN operation_cards oc ON o.id = oc.operation_id AND p.id = oc.part_id
        WHERE oc.operation_date BETWEEN ? AND ?
        GROUP BY p.id, o.id, po.standard_time, o.cost_per_hour
        HAVING COUNT(oc.id) > 0
        ORDER BY variance DESC
        """
        
        success, results = self.db.execute_query(query, [from_date, to_date])
        
        if success:
            columns = ['الجزء', 'العملية', 'الوقت المعياري', 'متوسط الوقت الفعلي', 'التكلفة/ساعة', 'التكلفة المعيارية', 'متوسط التكلفة الفعلية', 'الانحراف']
            # تنسيق النتائج
            formatted_results = []
            for row in results:
                formatted_row = list(row)
                # تنسيق الأرقام
                for i in [2, 3, 4, 5, 6, 7]:
                    if formatted_row[i] is not None:
                        formatted_row[i] = f"{float(formatted_row[i]):.2f}"
                formatted_results.append(formatted_row)
            
            self.display_results(columns, formatted_results)
        else:
            messagebox.showerror("خطأ", "فشل في تنفيذ الاستعلام")
    
    def material_consumption_report(self):
        """تقرير استهلاك المواد الخام"""
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        
        query = """
        SELECT mi.material_name, mi.unit,
               SUM(mi.quantity) as total_quantity,
               AVG(mi.unit_cost) as avg_unit_cost,
               SUM(mi.total_cost) as total_cost,
               COUNT(DISTINCT mi.work_order_id) as orders_count
        FROM material_issues mi
        WHERE mi.issue_date BETWEEN ? AND ?
        GROUP BY mi.material_name, mi.unit
        ORDER BY total_cost DESC
        """
        
        success, results = self.db.execute_query(query, [from_date, to_date])
        
        if success:
            columns = ['اسم المادة', 'الوحدة', 'إجمالي الكمية', 'متوسط سعر الوحدة', 'إجمالي التكلفة', 'عدد الأوامر']
            # تنسيق النتائج
            formatted_results = []
            for row in results:
                formatted_row = list(row)
                formatted_row[2] = f"{float(formatted_row[2]):.2f}"  # الكمية
                formatted_row[3] = f"{float(formatted_row[3]):.2f}"  # متوسط السعر
                formatted_row[4] = f"{float(formatted_row[4]):.2f}"  # إجمالي التكلفة
                formatted_results.append(formatted_row)
            
            self.display_results(columns, formatted_results)
        else:
            messagebox.showerror("خطأ", "فشل في تنفيذ الاستعلام")
    
    def defective_items_report(self):
        """تقرير القطاعات المعيبة"""
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        
        query = """
        SELECT di.defect_type, pt.name as part_name,
               COUNT(*) as defect_count,
               SUM(di.quantity) as total_defective_quantity,
               SUM(di.cost_impact) as total_cost_impact,
               AVG(di.cost_impact) as avg_cost_impact
        FROM defective_items di
        JOIN parts pt ON di.part_id = pt.id
        WHERE di.defect_date BETWEEN ? AND ?
        GROUP BY di.defect_type, pt.name
        ORDER BY total_cost_impact DESC
        """
        
        success, results = self.db.execute_query(query, [from_date, to_date])
        
        if success:
            columns = ['نوع العيب', 'الجزء', 'عدد الحالات', 'إجمالي الكمية المعيبة', 'إجمالي تأثير التكلفة', 'متوسط تأثير التكلفة']
            # تنسيق النتائج
            formatted_results = []
            for row in results:
                formatted_row = list(row)
                formatted_row[3] = f"{float(formatted_row[3]):.2f}"  # الكمية
                formatted_row[4] = f"{float(formatted_row[4]):.2f}"  # إجمالي التأثير
                formatted_row[5] = f"{float(formatted_row[5]):.2f}"  # متوسط التأثير
                formatted_results.append(formatted_row)
            
            self.display_results(columns, formatted_results)
        else:
            messagebox.showerror("خطأ", "فشل في تنفيذ الاستعلام")
    
    def operations_efficiency_report(self):
        """تقرير كفاءة العمليات"""
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        
        query = """
        SELECT o.name as operation_name, w.name as workshop_name,
               COUNT(oc.id) as operations_count,
               AVG(oc.actual_time) as avg_actual_time,
               AVG(po.standard_time) as avg_standard_time,
               (AVG(po.standard_time) / AVG(oc.actual_time) * 100) as efficiency_percentage
        FROM operations o
        JOIN workshops w ON o.workshop_id = w.id
        JOIN operation_cards oc ON o.id = oc.operation_id
        JOIN part_operations po ON o.id = po.operation_id
        WHERE oc.operation_date BETWEEN ? AND ?
        GROUP BY o.id, o.name, w.name
        HAVING COUNT(oc.id) >= 3
        ORDER BY efficiency_percentage DESC
        """
        
        success, results = self.db.execute_query(query, [from_date, to_date])
        
        if success:
            columns = ['العملية', 'الورشة', 'عدد العمليات', 'متوسط الوقت الفعلي', 'متوسط الوقت المعياري', 'نسبة الكفاءة %']
            # تنسيق النتائج
            formatted_results = []
            for row in results:
                formatted_row = list(row)
                formatted_row[3] = f"{float(formatted_row[3]):.2f}"  # الوقت الفعلي
                formatted_row[4] = f"{float(formatted_row[4]):.2f}"  # الوقت المعياري
                formatted_row[5] = f"{float(formatted_row[5]):.1f}%" if formatted_row[5] else "0.0%"  # الكفاءة
                formatted_results.append(formatted_row)
            
            self.display_results(columns, formatted_results)
        else:
            messagebox.showerror("خطأ", "فشل في تنفيذ الاستعلام")
    
    def product_cost_report(self):
        """تقرير تكلفة المنتجات"""
        messagebox.showinfo("معلومات", "تقرير تكلفة المنتجات قيد التطوير")
    
    def workshop_performance_report(self):
        """تقرير أداء الورش"""
        messagebox.showinfo("معلومات", "تقرير أداء الورش قيد التطوير")
    
    def inventory_consumption_report(self):
        """تقرير المخزون والاستهلاك"""
        messagebox.showinfo("معلومات", "تقرير المخزون والاستهلاك قيد التطوير")
    
    def cost_variance_report(self):
        """تقرير الانحرافات في التكلفة"""
        messagebox.showinfo("معلومات", "تقرير الانحرافات في التكلفة قيد التطوير")
    
    def production_summary_report(self):
        """تقرير ملخص الإنتاج"""
        messagebox.showinfo("معلومات", "تقرير ملخص الإنتاج قيد التطوير")
    
    def display_results(self, columns, results):
        """عرض النتائج في الجدول"""
        # مسح النتائج السابقة
        self.clear_results()
        
        # تعيين الأعمدة
        self.results_tree['columns'] = columns
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120, anchor='center')
        
        # إدراج البيانات
        for row in results:
            self.results_tree.insert('', 'end', values=row)
        
        # عرض عدد النتائج
        messagebox.showinfo("نتائج الاستعلام", f"تم العثور على {len(results)} نتيجة")
    
    def clear_results(self):
        """مسح النتائج"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.results_tree['columns'] = ()
    
    def export_results(self):
        """تصدير النتائج"""
        if not self.results_tree.get_children():
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
            return
        
        messagebox.showinfo("معلومات", "وظيفة التصدير قيد التطوير")

if __name__ == "__main__":
    app = QueriesWindow()
    app.window.mainloop()

import sqlite3
import os
from datetime import datetime
import shutil
from config import SystemConfig, DatabaseConfig

class DatabaseManager:
    def __init__(self, db_name=None):
        self.db_name = db_name or SystemConfig.DATABASE_NAME
        self.connection = None
        self.create_database()
    
    def connect(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(
                self.db_name,
                timeout=DatabaseConfig.CONNECTION_TIMEOUT
            )
            self.connection.row_factory = sqlite3.Row

            # تطبيق إعدادات PRAGMA
            for pragma in DatabaseConfig.get_pragma_settings():
                self.connection.execute(pragma)

            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
    
    def create_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        if self.connect():
            self.create_tables()
            self.disconnect()
    
    def create_tables(self):
        """إنشاء جميع الجداول المطلوبة"""
        
        # جدول المنتجات
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                code TEXT UNIQUE,
                description TEXT,
                unit TEXT,
                standard_cost REAL DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                active BOOLEAN DEFAULT 1
            )
        ''')
        
        # جدول الأجزاء
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT UNIQUE,
                description TEXT,
                unit TEXT,
                standard_cost REAL DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                active BOOLEAN DEFAULT 1
            )
        ''')
        
        # جدول القطاعات
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS sectors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                code TEXT UNIQUE,
                description TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                active BOOLEAN DEFAULT 1
            )
        ''')
        
        # جدول الورش
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS workshops (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT UNIQUE,
                sector_id INTEGER,
                description TEXT,
                overhead_rate REAL DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                active BOOLEAN DEFAULT 1,
                FOREIGN KEY (sector_id) REFERENCES sectors (id)
            )
        ''')
        
        # جدول عمليات التشغيل
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS operations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT UNIQUE,
                workshop_id INTEGER,
                description TEXT,
                standard_time REAL DEFAULT 0,
                cost_per_hour REAL DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                active BOOLEAN DEFAULT 1,
                FOREIGN KEY (workshop_id) REFERENCES workshops (id)
            )
        ''')
        
        # جدول علاقة المنتجات بالأجزاء
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS product_parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER,
                part_id INTEGER,
                quantity REAL NOT NULL,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (part_id) REFERENCES parts (id),
                UNIQUE(product_id, part_id)
            )
        ''')
        
        # جدول علاقة الأجزاء بالعمليات
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS part_operations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                part_id INTEGER,
                operation_id INTEGER,
                sequence_number INTEGER,
                standard_time REAL DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (part_id) REFERENCES parts (id),
                FOREIGN KEY (operation_id) REFERENCES operations (id),
                UNIQUE(part_id, operation_id)
            )
        ''')
        
        # جدول علاقة المنتجات بالأجزاء (BOM - Bill of Materials)
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS product_parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                part_id INTEGER NOT NULL,
                quantity REAL NOT NULL DEFAULT 1,
                unit TEXT NOT NULL DEFAULT 'قطعة',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
                FOREIGN KEY (part_id) REFERENCES parts (id) ON DELETE CASCADE,
                UNIQUE(product_id, part_id)
            )
        ''')

        # جدول أوامر التشغيل
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS work_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_number TEXT NOT NULL UNIQUE,
                product_id INTEGER,
                quantity REAL NOT NULL,
                start_date DATE,
                end_date DATE,
                status TEXT DEFAULT 'جديد',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # جدول بطاقة التشغيل
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS operation_cards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                work_order_id INTEGER,
                part_id INTEGER,
                operation_id INTEGER,
                actual_time REAL DEFAULT 0,
                actual_cost REAL DEFAULT 0,
                operator_name TEXT,
                operation_date DATE,
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (work_order_id) REFERENCES work_orders (id),
                FOREIGN KEY (part_id) REFERENCES parts (id),
                FOREIGN KEY (operation_id) REFERENCES operations (id)
            )
        ''')
        
        # جدول صرف الخامات
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS material_issues (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                work_order_id INTEGER,
                material_name TEXT NOT NULL,
                quantity REAL NOT NULL,
                unit TEXT,
                unit_cost REAL DEFAULT 0,
                total_cost REAL DEFAULT 0,
                issue_date DATE,
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (work_order_id) REFERENCES work_orders (id)
            )
        ''')
        
        # جدول القطاعات غير السليمة
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS defective_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                work_order_id INTEGER,
                part_id INTEGER,
                quantity REAL NOT NULL,
                defect_type TEXT,
                defect_reason TEXT,
                cost_impact REAL DEFAULT 0,
                defect_date DATE,
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (work_order_id) REFERENCES work_orders (id),
                FOREIGN KEY (part_id) REFERENCES parts (id)
            )
        ''')
        
        # جدول السنوات المالية
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS fiscal_years (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                year_name TEXT NOT NULL UNIQUE,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                is_current BOOLEAN DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المستخدمين
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                full_name TEXT,
                role TEXT DEFAULT 'مستخدم',
                active BOOLEAN DEFAULT 1,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.connection.commit()
    
    def backup_database(self, backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if backup_path is None:
                backup_dir = SystemConfig.get_backup_path()
                backup_filename = SystemConfig.generate_backup_filename()
                backup_path = os.path.join(backup_dir, backup_filename)

            shutil.copy2(self.db_name, backup_path)

            # تنظيف النسخ الاحتياطية القديمة
            self._cleanup_old_backups()

            return True, f"تم إنشاء النسخة الاحتياطية: {backup_path}"
        except Exception as e:
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {e}"

    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backup_dir = SystemConfig.get_backup_path()
            backup_files = []

            for file in os.listdir(backup_dir):
                if file.startswith("backup_") and file.endswith(".db"):
                    file_path = os.path.join(backup_dir, file)
                    backup_files.append((file_path, os.path.getctime(file_path)))

            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # حذف النسخ الزائدة
            if len(backup_files) > SystemConfig.MAX_BACKUP_FILES:
                for file_path, _ in backup_files[SystemConfig.MAX_BACKUP_FILES:]:
                    os.remove(file_path)

        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
    
    def restore_database(self, backup_path):
        """استرجاع قاعدة البيانات من نسخة احتياطية"""
        try:
            if os.path.exists(backup_path):
                self.disconnect()
                shutil.copy2(backup_path, self.db_name)
                return True, "تم استرجاع قاعدة البيانات بنجاح"
            else:
                return False, "ملف النسخة الاحتياطية غير موجود"
        except Exception as e:
            return False, f"خطأ في استرجاع قاعدة البيانات: {e}"
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام قاعدة البيانات"""
        try:
            if self.connect():
                cursor = self.connection.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    result = cursor.fetchall()
                else:
                    self.connection.commit()
                    result = cursor.rowcount
                
                self.disconnect()
                return True, result
        except Exception as e:
            self.disconnect()
            return False, str(e)
    
    def get_all_records(self, table_name, where_clause="", params=None):
        """جلب جميع السجلات من جدول معين"""
        query = f"SELECT * FROM {table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        success, result = self.execute_query(query, params)
        return result if success else []
    
    def insert_record(self, table_name, data):
        """إدراج سجل جديد"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        success, result = self.execute_query(query, list(data.values()))
        return success
    
    def update_record(self, table_name, data, where_clause, where_params):
        """تحديث سجل موجود"""
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        query = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"
        
        params = list(data.values()) + where_params
        success, result = self.execute_query(query, params)
        return success
    
    def delete_record(self, table_name, where_clause, where_params):
        """حذف سجل"""
        query = f"DELETE FROM {table_name} WHERE {where_clause}"
        success, result = self.execute_query(query, where_params)
        return success

@echo off
chcp 65001 >nul
title Industrial Cost System - Updated Version

echo.
echo ========================================================
echo Industrial Cost Management System - Updated Version
echo ========================================================
echo.
echo New Features:
echo - Product-Parts Linking (BOM)
echo - Modern Professional Interface
echo - Enhanced Navigation
echo - Interactive Dashboard
echo.
echo ========================================================
echo Starting System...
echo ========================================================
echo.

REM Try to run the updated version
python START.py

REM If <PERSON> fails, try py
if errorlevel 1 (
    echo.
    echo Trying with py command...
    py START.py
)

REM If both fail
if errorlevel 1 (
    echo.
    echo Failed to start the system
    echo.
    echo Please check:
    echo - Python is installed correctly
    echo - All system files are present
    echo - File permissions are correct
    echo.
    pause
)

echo.
echo Thank you for using the system
pause

# -*- coding: utf-8 -*-
"""
ملف التكوين لنظام التكاليف الصناعي المتكامل
Configuration file for Industrial Cost Management System
"""

import os
from datetime import datetime

class SystemConfig:
    """إعدادات النظام الأساسية"""
    
    # معلومات النظام
    SYSTEM_NAME = "نظام التكاليف الصناعي المتكامل"
    SYSTEM_NAME_EN = "Industrial Cost Management System"
    VERSION = "1.0"
    BUILD_DATE = "2024-12-19"
    DEVELOPER = "فريق تطوير النظام"
    
    # إعدادات قاعدة البيانات
    DATABASE_NAME = "industrial_costs.db"
    BACKUP_FOLDER = "backups"
    AUTO_BACKUP = True
    BACKUP_INTERVAL_DAYS = 7
    MAX_BACKUP_FILES = 10
    
    # إعدادات الواجهة
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    WINDOW_MIN_WIDTH = 800
    WINDOW_MIN_HEIGHT = 600
    
    # الخطوط
    ARABIC_FONT = ('Arial', 12)
    TITLE_FONT = ('Arial', 16, 'bold')
    SMALL_FONT = ('Arial', 10)
    LARGE_FONT = ('Arial', 14)
    
    # الألوان
    PRIMARY_COLOR = '#2c3e50'
    SECONDARY_COLOR = '#3498db'
    SUCCESS_COLOR = '#27ae60'
    WARNING_COLOR = '#f39c12'
    ERROR_COLOR = '#e74c3c'
    BACKGROUND_COLOR = '#f0f0f0'
    TEXT_COLOR = '#2c3e50'
    
    # إعدادات التقارير
    REPORTS_FOLDER = "reports"
    EXPORT_FOLDER = "exports"
    DEFAULT_REPORT_FORMAT = "PDF"
    SUPPORTED_FORMATS = ["PDF", "Excel", "CSV"]
    
    # إعدادات السجلات
    LOGS_FOLDER = "logs"
    LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    LOG_FILE_SIZE_MB = 10
    LOG_BACKUP_COUNT = 5
    
    # إعدادات الأمان
    ENABLE_USER_AUTHENTICATION = False
    SESSION_TIMEOUT_MINUTES = 30
    PASSWORD_MIN_LENGTH = 6
    ENABLE_AUDIT_LOG = True
    
    # إعدادات النظام
    AUTO_SAVE_INTERVAL_MINUTES = 5
    CONFIRM_DELETE_OPERATIONS = True
    SHOW_SPLASH_SCREEN = True
    SPLASH_DURATION_SECONDS = 3
    
    # السنوات المالية المتاحة
    FISCAL_YEARS = [
        "2024/2025",
        "2023/2024", 
        "2022/2023",
        "2021/2022"
    ]
    DEFAULT_FISCAL_YEAR = "2024/2025"
    
    # وحدات القياس المتاحة
    UNITS = [
        "قطعة",
        "كيلو",
        "جرام",
        "طن",
        "متر",
        "سنتيمتر",
        "لتر",
        "مليلتر",
        "صندوق",
        "كرتونة",
        "دزينة",
        "مجموعة"
    ]
    
    # أنواع العيوب
    DEFECT_TYPES = [
        "عيب في المواد",
        "خطأ في التشغيل",
        "عطل في الآلة",
        "خطأ بشري",
        "عيب في التصميم",
        "مشكلة في الجودة",
        "أخرى"
    ]
    
    # حالات أوامر التشغيل
    WORK_ORDER_STATUSES = [
        "جديد",
        "قيد التنفيذ",
        "مكتمل",
        "متوقف",
        "ملغي"
    ]
    
    # أدوار المستخدمين
    USER_ROLES = [
        "مدير النظام",
        "مدير الإنتاج",
        "محاسب التكاليف",
        "مشرف الورشة",
        "مستخدم عادي"
    ]
    
    @classmethod
    def get_database_path(cls):
        """الحصول على مسار قاعدة البيانات"""
        return os.path.join(os.getcwd(), cls.DATABASE_NAME)
    
    @classmethod
    def get_backup_path(cls):
        """الحصول على مسار مجلد النسخ الاحتياطية"""
        backup_dir = os.path.join(os.getcwd(), cls.BACKUP_FOLDER)
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        return backup_dir
    
    @classmethod
    def get_reports_path(cls):
        """الحصول على مسار مجلد التقارير"""
        reports_dir = os.path.join(os.getcwd(), cls.REPORTS_FOLDER)
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        return reports_dir
    
    @classmethod
    def get_exports_path(cls):
        """الحصول على مسار مجلد التصدير"""
        exports_dir = os.path.join(os.getcwd(), cls.EXPORT_FOLDER)
        if not os.path.exists(exports_dir):
            os.makedirs(exports_dir)
        return exports_dir
    
    @classmethod
    def get_logs_path(cls):
        """الحصول على مسار مجلد السجلات"""
        logs_dir = os.path.join(os.getcwd(), cls.LOGS_FOLDER)
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        return logs_dir
    
    @classmethod
    def generate_backup_filename(cls):
        """إنشاء اسم ملف النسخة الاحتياطية"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"backup_{timestamp}_{cls.DATABASE_NAME}"
    
    @classmethod
    def get_system_info(cls):
        """الحصول على معلومات النظام"""
        return {
            'name': cls.SYSTEM_NAME,
            'name_en': cls.SYSTEM_NAME_EN,
            'version': cls.VERSION,
            'build_date': cls.BUILD_DATE,
            'developer': cls.DEVELOPER,
            'database': cls.DATABASE_NAME,
            'current_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

class DatabaseConfig:
    """إعدادات قاعدة البيانات المتقدمة"""
    
    # إعدادات الاتصال
    CONNECTION_TIMEOUT = 30
    BUSY_TIMEOUT = 30000  # milliseconds
    
    # إعدادات الأداء
    CACHE_SIZE = 2000
    PAGE_SIZE = 4096
    SYNCHRONOUS = "NORMAL"  # OFF, NORMAL, FULL
    JOURNAL_MODE = "WAL"    # DELETE, TRUNCATE, PERSIST, MEMORY, WAL, OFF
    
    # إعدادات الأمان
    FOREIGN_KEYS = True
    RECURSIVE_TRIGGERS = True
    
    @classmethod
    def get_pragma_settings(cls):
        """الحصول على إعدادات PRAGMA لقاعدة البيانات"""
        return [
            f"PRAGMA cache_size = {cls.CACHE_SIZE}",
            f"PRAGMA page_size = {cls.PAGE_SIZE}",
            f"PRAGMA synchronous = {cls.SYNCHRONOUS}",
            f"PRAGMA journal_mode = {cls.JOURNAL_MODE}",
            f"PRAGMA foreign_keys = {'ON' if cls.FOREIGN_KEYS else 'OFF'}",
            f"PRAGMA recursive_triggers = {'ON' if cls.RECURSIVE_TRIGGERS else 'OFF'}",
            f"PRAGMA busy_timeout = {cls.BUSY_TIMEOUT}"
        ]

class UIConfig:
    """إعدادات واجهة المستخدم"""
    
    # إعدادات الجداول
    TABLE_ROW_HEIGHT = 25
    TABLE_HEADER_HEIGHT = 30
    TABLE_SELECTION_COLOR = '#e3f2fd'
    TABLE_ALTERNATE_COLOR = '#f5f5f5'
    
    # إعدادات النماذج
    FORM_PADDING = 10
    BUTTON_WIDTH = 12
    ENTRY_WIDTH = 30
    COMBO_WIDTH = 20
    
    # إعدادات الرسائل
    MESSAGE_DURATION = 3000  # milliseconds
    TOOLTIP_DELAY = 500      # milliseconds
    
    # إعدادات التحديث التلقائي
    AUTO_REFRESH_INTERVAL = 30000  # milliseconds
    SEARCH_DELAY = 500             # milliseconds

# تصدير الإعدادات
__all__ = ['SystemConfig', 'DatabaseConfig', 'UIConfig']

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple launcher for the Industrial Cost Management System
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Industrial Cost Management System - Starting...")

try:
    # Try to run modern interface
    from modern_main_window import ModernMainWindow
    from database import DatabaseManager
    
    # Initialize database
    db = DatabaseManager()
    db.initialize_database()
    
    # Run system
    app = ModernMainWindow()
    app.run()
    
except:
    # Fallback to classic interface
    print("Loading classic interface...")
    
    try:
        from main_window import MainWindow
        from database import DatabaseManager
        
        db = DatabaseManager()
        db.initialize_database()
        
        app = MainWindow()
        app.run()
        
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

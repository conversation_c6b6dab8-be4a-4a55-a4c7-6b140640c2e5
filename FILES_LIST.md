# 📁 قائمة ملفات النظام المحدث

## 🚀 ملفات التشغيل الرئيسية

### ⭐ للتشغيل السريع (موصى به):
- ✅ `START.py` - التشغيل السريع للنسخة المحدثة
- ✅ `تشغيل_النسخة_المحدثة.bat` - ملف batch للتشغيل على Windows

### 🎯 ملفات التشغيل المتخصصة:
- ✅ `run_updated.py` - تشغيل النسخة المحدثة مباشرة
- ✅ `start_system.py` - نافذة اختيار نوع الواجهة
- ✅ `run_modern.py` - تشغيل الواجهة المودرن فقط
- ✅ `run_classic.py` - تشغيل الواجهة الكلاسيكية فقط
- ✅ `main.py` - التشغيل الرئيسي (افتراضياً مودرن)

---

## 🏗️ ملفات النظام الأساسية

### 🔧 الملفات الأساسية:
- ✅ `database.py` - إدارة قاعدة البيانات
- ✅ `config.py` - إعدادات النظام المحدثة
- ✅ `modern_styles.py` - أنماط الواجهة المودرن (جديد)

### 🖥️ النوافذ الرئيسية:
- ✅ `main_window.py` - النافذة الرئيسية الكلاسيكية
- ✅ `modern_main_window.py` - النافذة الرئيسية المودرن (جديد)

---

## 📊 نوافذ البيانات الأساسية

- ✅ `products_window.py` - إدارة المنتجات
- ✅ `parts_window.py` - إدارة الأجزاء
- ✅ `sectors_window.py` - إدارة القطاعات
- ✅ `workshops_window.py` - إدارة الورش
- ✅ `operations_window.py` - إدارة العمليات
- ✅ `part_operations_window.py` - ربط الأجزاء بالعمليات
- ✅ `product_parts_window.py` - ربط المنتجات بالأجزاء (جديد)

---

## 🔄 نوافذ بيانات الحركة

- ✅ `work_orders_window.py` - أوامر التشغيل
- ✅ `operation_cards_window.py` - بطاقات التشغيل
- ✅ `material_issues_window.py` - صرف المواد
- ✅ `defective_items_window.py` - القطاعات المعيبة

---

## 📈 نوافذ التقارير والاستعلامات

- ✅ `queries_window.py` - الاستعلامات
- ✅ `reports_window.py` - التقارير

---

## 📚 ملفات التوثيق

- ✅ `README.md` - دليل النظام الرئيسي (محدث)
- ✅ `HOW_TO_RUN.md` - دليل التشغيل المفصل (جديد)
- ✅ `FILES_LIST.md` - قائمة الملفات هذه (جديد)

---

## 🗂️ الملفات والمجلدات التي تُنشأ تلقائياً

### قاعدة البيانات:
- `industrial_costs.db` - قاعدة البيانات الرئيسية (تُنشأ تلقائياً)

### المجلدات:
- `backups/` - النسخ الاحتياطية (يُنشأ تلقائياً)
- `reports/` - التقارير المُصدرة (يُنشأ تلقائياً)
- `exports/` - الملفات المُصدرة (يُنشأ تلقائياً)
- `logs/` - ملفات السجلات (يُنشأ تلقائياً)

---

## ✅ فحص الملفات المطلوبة

### للتأكد من وجود جميع الملفات، شغل:
```bash
python -c "
import os
required_files = [
    'START.py', 'database.py', 'config.py',
    'main_window.py', 'modern_main_window.py', 'modern_styles.py',
    'product_parts_window.py', 'products_window.py', 'parts_window.py'
]
missing = [f for f in required_files if not os.path.exists(f)]
if missing:
    print('❌ ملفات مفقودة:', missing)
else:
    print('✅ جميع الملفات الأساسية موجودة')
"
```

---

## 🔄 ترقية من النسخة السابقة

إذا كان لديك النسخة السابقة، تأكد من إضافة الملفات الجديدة:

### الملفات الجديدة المطلوبة:
1. ✅ `product_parts_window.py` - نافذة ربط المنتجات بالأجزاء
2. ✅ `modern_styles.py` - أنماط الواجهة المودرن
3. ✅ `modern_main_window.py` - النافذة الرئيسية المودرن
4. ✅ `START.py` - ملف التشغيل السريع
5. ✅ `run_updated.py` - تشغيل النسخة المحدثة
6. ✅ `start_system.py` - نافذة اختيار الواجهة

### التحديثات على الملفات الموجودة:
1. ✅ `config.py` - إضافة إعدادات الواجهة المودرن
2. ✅ `database.py` - إضافة جدول `product_parts`
3. ✅ `main_window.py` - إضافة ربط نافذة المنتجات بالأجزاء
4. ✅ `main.py` - تحديث للتشغيل المودرن افتراضياً

---

## 🎯 ملاحظات مهمة

1. **جميع الملفات يجب أن تكون في نفس المجلد**
2. **لا تحتاج لتثبيت مكتبات إضافية** (tkinter و sqlite3 مدمجة مع Python)
3. **النظام يعمل على Windows 7+ (32/64 bit)**
4. **يُنصح بتشغيل النظام من خلال `START.py`**
5. **في حالة وجود مشاكل، راجع `HOW_TO_RUN.md`**

---

**🏭 نظام التكاليف الصناعي المتكامل - النسخة المحدثة 2.0**

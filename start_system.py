#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل الرئيسي للنظام المحدث
Main launcher for the updated system

نظام التكاليف الصناعي المتكامل - النسخة المحدثة
Industrial Cost Management System - Updated Version
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox, ttk

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def show_launcher():
    """عرض نافذة اختيار نوع الواجهة"""
    
    launcher = tk.Tk()
    launcher.title("نظام التكاليف الصناعي المتكامل")
    launcher.geometry("600x400")
    launcher.configure(bg='#f8f9fa')
    launcher.resizable(False, False)
    
    # توسيط النافذة
    launcher.eval('tk::PlaceWindow . center')
    
    # الإطار الرئيسي
    main_frame = tk.Frame(launcher, bg='#f8f9fa')
    main_frame.pack(fill='both', expand=True, padx=30, pady=30)
    
    # العنوان
    title_label = tk.Label(main_frame,
                          text="🏭 نظام التكاليف الصناعي المتكامل",
                          bg='#f8f9fa',
                          fg='#2c3e50',
                          font=('Arial', 20, 'bold'))
    title_label.pack(pady=(0, 10))
    
    # العنوان الفرعي
    subtitle_label = tk.Label(main_frame,
                             text="Industrial Cost Management System",
                             bg='#f8f9fa',
                             fg='#6c757d',
                             font=('Arial', 12))
    subtitle_label.pack(pady=(0, 30))
    
    # وصف النسخة المحدثة
    description_label = tk.Label(main_frame,
                                text="النسخة المحدثة تتضمن:\n"
                                     "• ربط المنتجات بالأجزاء (BOM)\n"
                                     "• واجهة مودرن واحترافية\n"
                                     "• تنقلات محسنة\n"
                                     "• لوحة معلومات تفاعلية",
                                bg='#f8f9fa',
                                fg='#495057',
                                font=('Arial', 11),
                                justify='right')
    description_label.pack(pady=(0, 30))
    
    # إطار الأزرار
    buttons_frame = tk.Frame(main_frame, bg='#f8f9fa')
    buttons_frame.pack(pady=20)
    
    # زر الواجهة المودرن
    modern_btn = tk.Button(buttons_frame,
                          text="🎨 الواجهة المودرن\n(موصى به)",
                          bg='#007bff',
                          fg='white',
                          font=('Arial', 12, 'bold'),
                          width=20,
                          height=3,
                          border=0,
                          command=lambda: start_modern(launcher))
    modern_btn.pack(side='left', padx=10)
    
    # زر الواجهة الكلاسيكية
    classic_btn = tk.Button(buttons_frame,
                           text="📋 الواجهة الكلاسيكية\n(التقليدية)",
                           bg='#6c757d',
                           fg='white',
                           font=('Arial', 12, 'bold'),
                           width=20,
                           height=3,
                           border=0,
                           command=lambda: start_classic(launcher))
    classic_btn.pack(side='left', padx=10)
    
    # معلومات إضافية
    info_frame = tk.Frame(main_frame, bg='#e9ecef', relief='flat', bd=1)
    info_frame.pack(fill='x', pady=(30, 0))
    
    info_label = tk.Label(info_frame,
                         text="💡 نصيحة: اختر الواجهة المودرن للحصول على أفضل تجربة استخدام",
                         bg='#e9ecef',
                         fg='#495057',
                         font=('Arial', 10),
                         pady=10)
    info_label.pack()
    
    # معلومات النسخة
    version_frame = tk.Frame(main_frame, bg='#f8f9fa')
    version_frame.pack(side='bottom', fill='x', pady=(20, 0))
    
    version_label = tk.Label(version_frame,
                            text="الإصدار 2.0 - النسخة المحدثة | تطوير: فريق النظام",
                            bg='#f8f9fa',
                            fg='#6c757d',
                            font=('Arial', 9))
    version_label.pack()
    
    launcher.mainloop()

def start_modern(launcher_window):
    """تشغيل النظام بالواجهة المودرن"""
    try:
        launcher_window.destroy()
        
        # عرض رسالة التحميل
        loading = tk.Tk()
        loading.title("جاري التحميل...")
        loading.geometry("300x100")
        loading.configure(bg='#f8f9fa')
        loading.resizable(False, False)
        loading.eval('tk::PlaceWindow . center')
        
        loading_label = tk.Label(loading,
                                text="🔄 جاري تحميل الواجهة المودرن...",
                                bg='#f8f9fa',
                                fg='#007bff',
                                font=('Arial', 11))
        loading_label.pack(expand=True)
        
        loading.update()
        
        # استيراد وتشغيل النظام المودرن
        from modern_main_window import ModernMainWindow
        from database import DatabaseManager
        
        # تهيئة قاعدة البيانات
        db = DatabaseManager()
        success, message = db.initialize_database()
        
        loading.destroy()
        
        if not success:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في تهيئة قاعدة البيانات:\n{message}")
            return
        
        # تشغيل النظام
        app = ModernMainWindow()
        app.run()
        
    except ImportError as e:
        messagebox.showerror("خطأ", f"فشل في تحميل الواجهة المودرن:\n{e}\n\nتأكد من وجود ملف modern_main_window.py")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ أثناء تشغيل النظام:\n{e}")

def start_classic(launcher_window):
    """تشغيل النظام بالواجهة الكلاسيكية"""
    try:
        launcher_window.destroy()
        
        # عرض رسالة التحميل
        loading = tk.Tk()
        loading.title("جاري التحميل...")
        loading.geometry("300x100")
        loading.configure(bg='#f8f9fa')
        loading.resizable(False, False)
        loading.eval('tk::PlaceWindow . center')
        
        loading_label = tk.Label(loading,
                                text="🔄 جاري تحميل الواجهة الكلاسيكية...",
                                bg='#f8f9fa',
                                fg='#6c757d',
                                font=('Arial', 11))
        loading_label.pack(expand=True)
        
        loading.update()
        
        # استيراد وتشغيل النظام الكلاسيكي
        from main_window import MainWindow
        from database import DatabaseManager
        
        # تهيئة قاعدة البيانات
        db = DatabaseManager()
        success, message = db.initialize_database()
        
        loading.destroy()
        
        if not success:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في تهيئة قاعدة البيانات:\n{message}")
            return
        
        # تشغيل النظام
        app = MainWindow()
        app.run()
        
    except ImportError as e:
        messagebox.showerror("خطأ", f"فشل في تحميل الواجهة الكلاسيكية:\n{e}\n\nتأكد من وجود ملف main_window.py")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ أثناء تشغيل النظام:\n{e}")

def check_files():
    """فحص وجود الملفات المطلوبة"""
    required_files = [
        'database.py',
        'config.py',
        'main_window.py',
        'modern_main_window.py',
        'modern_styles.py',
        'product_parts_window.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        error_msg = "الملفات التالية مفقودة:\n\n"
        for file in missing_files:
            error_msg += f"• {file}\n"
        error_msg += "\nيرجى التأكد من وجود جميع ملفات النظام في نفس المجلد."
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("ملفات مفقودة", error_msg)
        root.destroy()
        return False
    
    return True

if __name__ == "__main__":
    print("🏭 نظام التكاليف الصناعي المتكامل - النسخة المحدثة")
    print("=" * 60)
    print("جاري فحص الملفات...")
    
    if check_files():
        print("✅ جميع الملفات موجودة")
        print("🚀 جاري تشغيل نافذة الاختيار...")
        print("=" * 60)
        show_launcher()
    else:
        print("❌ بعض الملفات مفقودة")
        input("اضغط Enter للخروج...")

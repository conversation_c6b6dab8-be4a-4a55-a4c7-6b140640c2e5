#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثبت نظام التكاليف الصناعي المتكامل
Industrial Cost Management System Installer
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

class SystemInstaller:
    def __init__(self):
        self.system_name = "نظام التكاليف الصناعي المتكامل"
        self.version = "1.0"
        self.required_files = [
            'run_system.py',
            'main_window.py',
            'database.py',
            'products_window.py',
            'config.py',
            'test_system.py',
            'README.md',
            'requirements.txt'
        ]
        self.folders_to_create = [
            'backups',
            'reports', 
            'exports',
            'logs'
        ]
        
    def print_header(self):
        """طباعة رأس المثبت"""
        print("=" * 60)
        print(f"   {self.system_name}")
        print(f"   Industrial Cost Management System")
        print(f"   الإصدار {self.version}")
        print("=" * 60)
        print()
    
    def check_python_version(self):
        """فحص إصدار Python"""
        print("🔍 فحص إصدار Python...")
        
        if sys.version_info < (3, 6):
            print(f"❌ خطأ: يتطلب النظام Python 3.6 أو أحدث")
            print(f"   الإصدار الحالي: {sys.version}")
            print("   يرجى تحديث Python من: https://www.python.org/downloads/")
            return False
        
        print(f"✅ Python {sys.version.split()[0]} - متوافق")
        return True
    
    def check_required_modules(self):
        """فحص المكتبات المطلوبة"""
        print("\n🔍 فحص المكتبات المطلوبة...")
        
        required_modules = [
            ('tkinter', 'واجهة المستخدم الرسومية'),
            ('sqlite3', 'قاعدة البيانات'),
            ('datetime', 'التعامل مع التواريخ'),
            ('os', 'عمليات نظام التشغيل'),
            ('shutil', 'عمليات الملفات')
        ]
        
        missing_modules = []
        
        for module, description in required_modules:
            try:
                __import__(module)
                print(f"✅ {module} - {description}")
            except ImportError:
                print(f"❌ {module} - غير متوفر")
                missing_modules.append(module)
        
        if missing_modules:
            print(f"\n❌ المكتبات المفقودة: {', '.join(missing_modules)}")
            print("   يرجى تثبيت Python مع المكتبات الأساسية")
            return False
        
        return True
    
    def check_required_files(self):
        """فحص الملفات المطلوبة"""
        print("\n🔍 فحص الملفات المطلوبة...")
        
        missing_files = []
        
        for file in self.required_files:
            if os.path.exists(file):
                print(f"✅ {file}")
            else:
                print(f"❌ {file} - غير موجود")
                missing_files.append(file)
        
        if missing_files:
            print(f"\n❌ الملفات المفقودة: {', '.join(missing_files)}")
            print("   تأكد من وجود جميع ملفات النظام في نفس المجلد")
            return False
        
        return True
    
    def create_folders(self):
        """إنشاء المجلدات المطلوبة"""
        print("\n📁 إنشاء المجلدات...")
        
        for folder in self.folders_to_create:
            try:
                if not os.path.exists(folder):
                    os.makedirs(folder)
                    print(f"✅ تم إنشاء مجلد: {folder}")
                else:
                    print(f"✅ مجلد موجود: {folder}")
            except Exception as e:
                print(f"❌ فشل في إنشاء مجلد {folder}: {e}")
                return False
        
        return True
    
    def test_system(self):
        """اختبار النظام"""
        print("\n🧪 اختبار النظام...")
        
        try:
            # تشغيل اختبار سريع
            result = subprocess.run([
                sys.executable, 'test_system.py', '--quick'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ اختبار النظام نجح")
                return True
            else:
                print("❌ فشل في اختبار النظام")
                print(f"   الخطأ: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ انتهت مهلة اختبار النظام")
            return False
        except Exception as e:
            print(f"❌ خطأ في اختبار النظام: {e}")
            return False
    
    def create_shortcuts(self):
        """إنشاء اختصارات التشغيل"""
        print("\n🔗 إنشاء اختصارات التشغيل...")
        
        try:
            # إنشاء ملف batch للتشغيل السريع
            if not os.path.exists('تشغيل_النظام.bat'):
                print("✅ ملف batch موجود بالفعل")
            else:
                print("✅ ملف batch تم إنشاؤه مسبقاً")
            
            # إنشاء ملف تشغيل Python مباشر
            shortcut_content = f'''#!/usr/bin/env python3
# اختصار تشغيل {self.system_name}
import subprocess
import sys
import os

if __name__ == "__main__":
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    subprocess.run([sys.executable, "run_system.py"])
'''
            
            with open('start.py', 'w', encoding='utf-8') as f:
                f.write(shortcut_content)
            
            print("✅ تم إنشاء ملف start.py")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء الاختصارات: {e}")
            return False
    
    def create_desktop_shortcut(self):
        """إنشاء اختصار على سطح المكتب (Windows)"""
        print("\n🖥️ إنشاء اختصار سطح المكتب...")
        
        try:
            if os.name == 'nt':  # Windows
                import winshell
                from win32com.client import Dispatch
                
                desktop = winshell.desktop()
                path = os.path.join(desktop, f"{self.system_name}.lnk")
                target = os.path.join(os.getcwd(), "تشغيل_النظام.bat")
                
                shell = Dispatch('WScript.Shell')
                shortcut = shell.CreateShortCut(path)
                shortcut.Targetpath = target
                shortcut.WorkingDirectory = os.getcwd()
                shortcut.IconLocation = target
                shortcut.save()
                
                print("✅ تم إنشاء اختصار سطح المكتب")
                return True
            else:
                print("ℹ️ إنشاء اختصار سطح المكتب متاح فقط على Windows")
                return True
                
        except ImportError:
            print("ℹ️ لإنشاء اختصار سطح المكتب، ثبت: pip install pywin32 winshell")
            return True
        except Exception as e:
            print(f"⚠️ فشل في إنشاء اختصار سطح المكتب: {e}")
            return True  # ليس خطأ حرج
    
    def show_completion_message(self):
        """عرض رسالة إكمال التثبيت"""
        print("\n" + "=" * 60)
        print("🎉 تم تثبيت النظام بنجاح!")
        print("=" * 60)
        print()
        print("📋 طرق تشغيل النظام:")
        print("   1. انقر مرتين على: تشغيل_النظام.bat")
        print("   2. شغل الأمر: python run_system.py")
        print("   3. شغل الأمر: python start.py")
        print()
        print("📚 الملفات المهمة:")
        print("   • README.md - معلومات النظام")
        print("   • دليل_المستخدم.md - دليل الاستخدام")
        print("   • test_system.py - اختبار النظام")
        print()
        print("📁 المجلدات:")
        print("   • backups/ - النسخ الاحتياطية")
        print("   • reports/ - التقارير")
        print("   • logs/ - ملفات السجلات")
        print()
        print("🔧 للاختبار:")
        print("   python test_system.py")
        print()
        print("📞 للمساعدة:")
        print("   python run_system.py --help")
        print()
        print("=" * 60)
    
    def install(self):
        """تشغيل عملية التثبيت الكاملة"""
        self.print_header()
        
        steps = [
            ("فحص إصدار Python", self.check_python_version),
            ("فحص المكتبات المطلوبة", self.check_required_modules),
            ("فحص الملفات المطلوبة", self.check_required_files),
            ("إنشاء المجلدات", self.create_folders),
            ("اختبار النظام", self.test_system),
            ("إنشاء الاختصارات", self.create_shortcuts),
            ("إنشاء اختصار سطح المكتب", self.create_desktop_shortcut)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            print("-" * 40)
            
            if not step_func():
                print(f"\n❌ فشل في: {step_name}")
                print("   يرجى حل المشاكل أعلاه وإعادة المحاولة")
                return False
        
        self.show_completion_message()
        return True

def main():
    """الدالة الرئيسية"""
    installer = SystemInstaller()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("مثبت نظام التكاليف الصناعي المتكامل")
            print()
            print("الاستخدام:")
            print("  python install.py        # تشغيل التثبيت")
            print("  python install.py --help # عرض المساعدة")
            return
    
    print("مرحباً بك في مثبت نظام التكاليف الصناعي المتكامل")
    print()
    
    response = input("هل تريد المتابعة مع التثبيت؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        success = installer.install()
        if success:
            print("\n🎉 تم التثبيت بنجاح!")
            input("\nاضغط Enter للخروج...")
        else:
            print("\n❌ فشل التثبيت!")
            input("\nاضغط Enter للخروج...")
    else:
        print("تم إلغاء التثبيت.")

if __name__ == "__main__":
    main()

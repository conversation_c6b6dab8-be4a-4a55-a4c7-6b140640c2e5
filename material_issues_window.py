import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from database import DatabaseManager
from config import SystemConfig, UIConfig

class MaterialIssuesWindow:
    def __init__(self, parent=None):
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("صرف المواد الخام")
        self.window.geometry("1200x650")
        self.window.configure(bg=SystemConfig.BACKGROUND_COLOR)
        
        # الخطوط من الإعدادات
        self.arabic_font = SystemConfig.ARABIC_FONT
        self.title_font = SystemConfig.TITLE_FONT
        
        # قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغيرات النموذج
        self.selected_issue_id = None
        self.setup_variables()
        
        self.create_widgets()
        self.setup_layout()
        self.load_work_orders_combo()
        self.load_material_issues()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.work_order_var = tk.StringVar()
        self.material_name_var = tk.StringVar()
        self.quantity_var = tk.DoubleVar()
        self.unit_var = tk.StringVar()
        self.unit_cost_var = tk.DoubleVar()
        self.total_cost_var = tk.DoubleVar()
        self.issue_date_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # تعيين القيم الافتراضية
        today = date.today().strftime("%Y-%m-%d")
        self.issue_date_var.set(today)
        self.unit_var.set("كيلو")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.window)
        
        # شريط العنوان
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="صرف المواد الخام",
            font=self.title_font
        )
        
        # إطار الإدخال
        self.input_frame = ttk.LabelFrame(self.main_frame, text="بيانات صرف المواد", padding=10)
        
        # الصف الأول
        ttk.Label(self.input_frame, text="أمر التشغيل:", font=self.arabic_font).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.work_order_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.work_order_var,
            font=self.arabic_font,
            width=35,
            state="readonly"
        )
        self.work_order_combo.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="اسم المادة:", font=self.arabic_font).grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.material_name_entry = ttk.Entry(self.input_frame, textvariable=self.material_name_var, font=self.arabic_font, width=25)
        self.material_name_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # الصف الثاني
        ttk.Label(self.input_frame, text="الكمية:", font=self.arabic_font).grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.quantity_entry = ttk.Entry(self.input_frame, textvariable=self.quantity_var, font=self.arabic_font, width=35)
        self.quantity_entry.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الوحدة:", font=self.arabic_font).grid(row=1, column=2, sticky='e', padx=5, pady=5)
        self.unit_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.unit_var,
            values=SystemConfig.UNITS,
            font=self.arabic_font,
            width=22
        )
        self.unit_combo.grid(row=1, column=3, padx=5, pady=5)
        
        # الصف الثالث
        ttk.Label(self.input_frame, text="سعر الوحدة:", font=self.arabic_font).grid(row=2, column=0, sticky='e', padx=5, pady=5)
        self.unit_cost_entry = ttk.Entry(self.input_frame, textvariable=self.unit_cost_var, font=self.arabic_font, width=35)
        self.unit_cost_entry.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="التكلفة الإجمالية:", font=self.arabic_font).grid(row=2, column=2, sticky='e', padx=5, pady=5)
        self.total_cost_entry = ttk.Entry(self.input_frame, textvariable=self.total_cost_var, font=self.arabic_font, width=25)
        self.total_cost_entry.grid(row=2, column=3, padx=5, pady=5)
        
        # الصف الرابع
        ttk.Label(self.input_frame, text="تاريخ الصرف:", font=self.arabic_font).grid(row=3, column=0, sticky='e', padx=5, pady=5)
        self.issue_date_entry = ttk.Entry(self.input_frame, textvariable=self.issue_date_var, font=self.arabic_font, width=35)
        self.issue_date_entry.grid(row=3, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="ملاحظات:", font=self.arabic_font).grid(row=3, column=2, sticky='e', padx=5, pady=5)
        self.notes_entry = ttk.Entry(self.input_frame, textvariable=self.notes_var, font=self.arabic_font, width=25)
        self.notes_entry.grid(row=3, column=3, padx=5, pady=5)
        
        # إطار الأزرار
        self.buttons_frame = ttk.Frame(self.input_frame)
        self.buttons_frame.grid(row=4, column=0, columnspan=4, pady=20)
        
        self.add_btn = ttk.Button(
            self.buttons_frame,
            text="إضافة",
            command=self.add_material_issue,
            width=12
        )
        self.add_btn.pack(side='left', padx=5)
        
        self.update_btn = ttk.Button(
            self.buttons_frame,
            text="تحديث",
            command=self.update_material_issue,
            width=12,
            state='disabled'
        )
        self.update_btn.pack(side='left', padx=5)
        
        self.delete_btn = ttk.Button(
            self.buttons_frame,
            text="حذف",
            command=self.delete_material_issue,
            width=12,
            state='disabled'
        )
        self.delete_btn.pack(side='left', padx=5)
        
        self.clear_btn = ttk.Button(
            self.buttons_frame,
            text="مسح",
            command=self.clear_form,
            width=12
        )
        self.clear_btn.pack(side='left', padx=5)
        
        self.calculate_btn = ttk.Button(
            self.buttons_frame,
            text="حساب التكلفة",
            command=self.calculate_total_cost,
            width=12
        )
        self.calculate_btn.pack(side='left', padx=5)
        
        # إطار قائمة صرف المواد
        self.list_frame = ttk.LabelFrame(self.main_frame, text="قائمة صرف المواد", padding=10)
        
        # جدول صرف المواد
        columns = ('ID', 'أمر التشغيل', 'المنتج', 'اسم المادة', 'الكمية', 'الوحدة', 'سعر الوحدة', 'التكلفة الإجمالية', 'تاريخ الصرف', 'ملاحظات')
        self.issues_tree = ttk.Treeview(self.list_frame, columns=columns, show='headings', height=12)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.issues_tree.heading(col, text=col)
            if col == 'ID':
                self.issues_tree.column(col, width=50)
            elif col in ['أمر التشغيل', 'المنتج', 'اسم المادة']:
                self.issues_tree.column(col, width=120)
            elif col in ['الكمية', 'الوحدة', 'سعر الوحدة', 'التكلفة الإجمالية']:
                self.issues_tree.column(col, width=100)
            elif col == 'تاريخ الصرف':
                self.issues_tree.column(col, width=100)
            elif col == 'ملاحظات':
                self.issues_tree.column(col, width=150)
        
        # شريط التمرير
        self.scrollbar = ttk.Scrollbar(self.list_frame, orient='vertical', command=self.issues_tree.yview)
        self.issues_tree.configure(yscrollcommand=self.scrollbar.set)
        
        # ربط الأحداث
        self.issues_tree.bind('<<TreeviewSelect>>', self.on_issue_select)
        
        # ربط حساب التكلفة التلقائي
        self.quantity_var.trace('w', self.auto_calculate_total)
        self.unit_cost_var.trace('w', self.auto_calculate_total)
        
        # إطار البحث
        self.search_frame = ttk.LabelFrame(self.main_frame, text="البحث والتصفية", padding=10)
        
        ttk.Label(self.search_frame, text="البحث:", font=self.arabic_font).pack(side='left', padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, font=self.arabic_font, width=25)
        self.search_entry.pack(side='left', padx=5)
        
        self.search_btn = ttk.Button(
            self.search_frame,
            text="بحث",
            command=self.search_material_issues,
            width=10
        )
        self.search_btn.pack(side='left', padx=5)
        
        self.refresh_btn = ttk.Button(
            self.search_frame,
            text="تحديث",
            command=self.load_material_issues,
            width=10
        )
        self.refresh_btn.pack(side='left', padx=5)
        
        # ربط البحث بالكتابة
        self.search_var.trace('w', self.on_search_change)
        
    def setup_layout(self):
        """ترتيب عناصر الواجهة"""
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.title_frame.pack(fill='x', pady=(0, 10))
        self.title_label.pack()
        
        self.input_frame.pack(fill='x', pady=(0, 10))
        
        self.search_frame.pack(fill='x', pady=(0, 10))
        
        self.list_frame.pack(fill='both', expand=True)
        self.issues_tree.pack(side='left', fill='both', expand=True)
        self.scrollbar.pack(side='right', fill='y')
        
    def load_work_orders_combo(self):
        """تحميل قائمة أوامر التشغيل في الـ combobox"""
        query = """
        SELECT wo.id, wo.order_number, p.name as product_name 
        FROM work_orders wo 
        LEFT JOIN products p ON wo.product_id = p.id 
        WHERE wo.status IN ('جديد', 'قيد التنفيذ')
        ORDER BY wo.order_number
        """
        success, orders = self.db.execute_query(query)
        
        if success:
            order_values = [f"{order['id']} - {order['order_number']} ({order['product_name']})" for order in orders]
            self.work_order_combo['values'] = order_values
        
    def clear_form(self):
        """مسح النموذج"""
        self.work_order_var.set("")
        self.material_name_var.set("")
        self.quantity_var.set(0.0)
        self.unit_var.set("كيلو")
        self.unit_cost_var.set(0.0)
        self.total_cost_var.set(0.0)
        today = date.today().strftime("%Y-%m-%d")
        self.issue_date_var.set(today)
        self.notes_var.set("")
        self.selected_issue_id = None
        
        self.add_btn.config(state='normal')
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
        
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.work_order_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار أمر التشغيل")
            return False
        
        if not self.material_name_var.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال اسم المادة")
            return False
        
        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                return False
        except:
            messagebox.showerror("خطأ", "الكمية يجب أن تكون رقم صحيح")
            return False
        
        try:
            unit_cost = float(self.unit_cost_var.get())
            if unit_cost < 0:
                messagebox.showerror("خطأ", "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")
                return False
        except:
            messagebox.showerror("خطأ", "سعر الوحدة يجب أن يكون رقم صحيح")
            return False
        
        # التحقق من صحة التاريخ
        try:
            datetime.strptime(self.issue_date_var.get(), "%Y-%m-%d")
        except:
            messagebox.showerror("خطأ", "تنسيق التاريخ يجب أن يكون YYYY-MM-DD")
            return False
        
        return True
        
    def get_work_order_id_from_combo(self):
        """استخراج معرف أمر التشغيل من النص المختار"""
        work_order_text = self.work_order_var.get()
        if work_order_text:
            return int(work_order_text.split(' - ')[0])
        return None
        
    def calculate_total_cost(self):
        """حساب التكلفة الإجمالية"""
        try:
            quantity = float(self.quantity_var.get())
            unit_cost = float(self.unit_cost_var.get())
            total_cost = quantity * unit_cost
            self.total_cost_var.set(total_cost)
            messagebox.showinfo("تم الحساب", f"التكلفة الإجمالية: {total_cost:.2f}")
        except:
            messagebox.showerror("خطأ", "يجب إدخال قيم صحيحة للكمية وسعر الوحدة")
    
    def auto_calculate_total(self, *args):
        """حساب التكلفة الإجمالية تلقائياً"""
        try:
            quantity = float(self.quantity_var.get())
            unit_cost = float(self.unit_cost_var.get())
            total_cost = quantity * unit_cost
            self.total_cost_var.set(total_cost)
        except:
            pass  # تجاهل الأخطاء في الحساب التلقائي
        
    def add_material_issue(self):
        """إضافة صرف مادة جديد"""
        if not self.validate_form():
            return
        
        work_order_id = self.get_work_order_id_from_combo()
        if not work_order_id:
            messagebox.showerror("خطأ", "يجب اختيار أمر التشغيل")
            return
        
        data = {
            'work_order_id': work_order_id,
            'material_name': self.material_name_var.get().strip(),
            'quantity': self.quantity_var.get(),
            'unit': self.unit_var.get(),
            'unit_cost': self.unit_cost_var.get(),
            'total_cost': self.total_cost_var.get(),
            'issue_date': self.issue_date_var.get(),
            'notes': self.notes_var.get().strip()
        }
        
        try:
            success = self.db.insert_record('material_issues', data)
            if success:
                messagebox.showinfo("نجح", "تم إضافة صرف المادة بنجاح")
                self.clear_form()
                self.load_material_issues()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة صرف المادة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def update_material_issue(self):
        """تحديث صرف مادة موجود"""
        if not self.selected_issue_id:
            return
        
        if not self.validate_form():
            return
        
        work_order_id = self.get_work_order_id_from_combo()
        if not work_order_id:
            messagebox.showerror("خطأ", "يجب اختيار أمر التشغيل")
            return
        
        data = {
            'work_order_id': work_order_id,
            'material_name': self.material_name_var.get().strip(),
            'quantity': self.quantity_var.get(),
            'unit': self.unit_var.get(),
            'unit_cost': self.unit_cost_var.get(),
            'total_cost': self.total_cost_var.get(),
            'issue_date': self.issue_date_var.get(),
            'notes': self.notes_var.get().strip()
        }
        
        try:
            success = self.db.update_record('material_issues', data, 'id = ?', [self.selected_issue_id])
            if success:
                messagebox.showinfo("نجح", "تم تحديث صرف المادة بنجاح")
                self.clear_form()
                self.load_material_issues()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث صرف المادة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def delete_material_issue(self):
        """حذف صرف مادة"""
        if not self.selected_issue_id:
            return
        
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف صرف المادة؟")
        if result:
            try:
                success = self.db.delete_record('material_issues', 'id = ?', [self.selected_issue_id])
                if success:
                    messagebox.showinfo("نجح", "تم حذف صرف المادة بنجاح")
                    self.clear_form()
                    self.load_material_issues()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف صرف المادة")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def load_material_issues(self):
        """تحميل قائمة صرف المواد"""
        # مسح البيانات الحالية
        for item in self.issues_tree.get_children():
            self.issues_tree.delete(item)
        
        # جلب البيانات من قاعدة البيانات مع ربط الجداول
        query = """
        SELECT mi.*, wo.order_number, p.name as product_name 
        FROM material_issues mi 
        LEFT JOIN work_orders wo ON mi.work_order_id = wo.id 
        LEFT JOIN products p ON wo.product_id = p.id 
        ORDER BY mi.issue_date DESC, mi.created_date DESC
        """
        success, issues = self.db.execute_query(query)
        
        if success:
            for issue in issues:
                self.issues_tree.insert('', 'end', values=(
                    issue['id'],
                    issue['order_number'] or "",
                    issue['product_name'] or "",
                    issue['material_name'],
                    f"{issue['quantity']:.2f}",
                    issue['unit'],
                    f"{issue['unit_cost']:.2f}",
                    f"{issue['total_cost']:.2f}",
                    issue['issue_date'] or "",
                    issue['notes'] or ""
                ))
    
    def on_issue_select(self, event):
        """عند اختيار صرف من القائمة"""
        selection = self.issues_tree.selection()
        if selection:
            item = self.issues_tree.item(selection[0])
            values = item['values']
            
            self.selected_issue_id = values[0]
            
            # البحث عن أمر التشغيل المناسب في الـ combobox
            order_number = values[1]
            for order_option in self.work_order_combo['values']:
                if order_number in order_option:
                    self.work_order_var.set(order_option)
                    break
            
            self.material_name_var.set(values[3])
            self.quantity_var.set(float(values[4]))
            self.unit_var.set(values[5])
            self.unit_cost_var.set(float(values[6]))
            self.total_cost_var.set(float(values[7]))
            self.issue_date_var.set(values[8])
            self.notes_var.set(values[9])
            
            self.add_btn.config(state='disabled')
            self.update_btn.config(state='normal')
            self.delete_btn.config(state='normal')
    
    def search_material_issues(self):
        """البحث في صرف المواد"""
        search_term = self.search_var.get().strip()
        if not search_term:
            self.load_material_issues()
            return
        
        # مسح البيانات الحالية
        for item in self.issues_tree.get_children():
            self.issues_tree.delete(item)
        
        # البحث في قاعدة البيانات
        query = """
        SELECT mi.*, wo.order_number, p.name as product_name 
        FROM material_issues mi 
        LEFT JOIN work_orders wo ON mi.work_order_id = wo.id 
        LEFT JOIN products p ON wo.product_id = p.id 
        WHERE wo.order_number LIKE ? OR p.name LIKE ? OR mi.material_name LIKE ?
        ORDER BY mi.issue_date DESC, mi.created_date DESC
        """
        params = [f"%{search_term}%"] * 3
        
        success, issues = self.db.execute_query(query, params)
        
        if success:
            for issue in issues:
                self.issues_tree.insert('', 'end', values=(
                    issue['id'],
                    issue['order_number'] or "",
                    issue['product_name'] or "",
                    issue['material_name'],
                    f"{issue['quantity']:.2f}",
                    issue['unit'],
                    f"{issue['unit_cost']:.2f}",
                    f"{issue['total_cost']:.2f}",
                    issue['issue_date'] or "",
                    issue['notes'] or ""
                ))
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        self.window.after(500, self.search_material_issues)

if __name__ == "__main__":
    app = MaterialIssuesWindow()
    app.window.mainloop()

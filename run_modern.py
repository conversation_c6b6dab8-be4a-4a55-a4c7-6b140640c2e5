#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام بالواجهة المودرن
Run system with modern interface
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from modern_main_window import ModernMainWindow
    from database import DatabaseManager
    from config import SystemConfig
    
    if __name__ == "__main__":
        print("🏭 نظام التكاليف الصناعي المتكامل - الواجهة المودرن")
        print("=" * 60)
        print("جاري تشغيل النظام...")
        print("=" * 60)
        
        # تهيئة قاعدة البيانات
        db = DatabaseManager()
        success, message = db.initialize_database()
        
        if not success:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {message}")
            input("اضغط Enter للخروج...")
            sys.exit(1)
        
        # تشغيل النظام
        app = ModernMainWindow()
        app.run()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع ملفات النظام في نفس المجلد")
    input("اضغط Enter للخروج...")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل النظام: {e}")
    print("يرجى مراجعة ملفات السجلات أو الاتصال بالدعم الفني")
    input("اضغط Enter للخروج...")

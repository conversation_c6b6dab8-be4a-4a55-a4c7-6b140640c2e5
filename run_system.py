#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التكاليف الصناعي المتكامل
Industrial Cost Management System

نظام محاسبة صناعية متكامل لإدارة التكاليف والإنتاج
يعمل على جميع أنظمة Windows من الإصدار 7 فما فوق

المطور: نظام التكاليف الصناعي
التاريخ: 2024
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_requirements():
    """فحص المتطلبات الأساسية للنظام"""
    try:
        # فحص Python version
        if sys.version_info < (3, 6):
            messagebox.showerror(
                "خطأ في الإصدار",
                "يتطلب النظام Python 3.6 أو أحدث\n"
                f"الإصدار الحالي: {sys.version}"
            )
            return False
        
        # فحص tkinter
        try:
            import tkinter as tk
        except ImportError:
            messagebox.showerror(
                "خطأ في المكتبات",
                "مكتبة tkinter غير متوفرة\n"
                "يرجى تثبيت Python مع tkinter"
            )
            return False
        
        # فحص sqlite3
        try:
            import sqlite3
        except ImportError:
            messagebox.showerror(
                "خطأ في المكتبات",
                "مكتبة sqlite3 غير متوفرة\n"
                "يرجى تثبيت Python مع sqlite3"
            )
            return False
        
        return True
        
    except Exception as e:
        messagebox.showerror(
            "خطأ في فحص المتطلبات",
            f"حدث خطأ أثناء فحص المتطلبات:\n{str(e)}"
        )
        return False

def setup_environment():
    """إعداد بيئة العمل"""
    try:
        # إنشاء مجلدات النظام إذا لم تكن موجودة
        folders = [
            'backups',
            'reports',
            'exports',
            'logs'
        ]
        
        for folder in folders:
            folder_path = os.path.join(current_dir, folder)
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
        
        return True
        
    except Exception as e:
        messagebox.showerror(
            "خطأ في إعداد البيئة",
            f"حدث خطأ أثناء إعداد بيئة العمل:\n{str(e)}"
        )
        return False

def show_splash_screen():
    """عرض شاشة البداية"""
    splash = tk.Tk()
    splash.title("نظام التكاليف الصناعي المتكامل")
    splash.geometry("500x300")
    splash.configure(bg='#2c3e50')
    splash.resizable(False, False)
    
    # توسيط النافذة
    splash.eval('tk::PlaceWindow . center')
    
    # إزالة شريط العنوان
    splash.overrideredirect(True)
    
    # إطار المحتوى
    content_frame = tk.Frame(splash, bg='#2c3e50')
    content_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # العنوان الرئيسي
    title_label = tk.Label(
        content_frame,
        text="نظام التكاليف الصناعي المتكامل",
        font=('Arial', 18, 'bold'),
        fg='white',
        bg='#2c3e50'
    )
    title_label.pack(pady=(20, 10))
    
    # العنوان الفرعي
    subtitle_label = tk.Label(
        content_frame,
        text="Industrial Cost Management System",
        font=('Arial', 12),
        fg='#ecf0f1',
        bg='#2c3e50'
    )
    subtitle_label.pack(pady=(0, 20))
    
    # وصف النظام
    description_label = tk.Label(
        content_frame,
        text="نظام محاسبة صناعية متكامل لإدارة التكاليف والإنتاج\n"
             "يدعم جميع أنظمة Windows من الإصدار 7 فما فوق",
        font=('Arial', 10),
        fg='#bdc3c7',
        bg='#2c3e50',
        justify='center'
    )
    description_label.pack(pady=(0, 30))
    
    # شريط التحميل
    progress_frame = tk.Frame(content_frame, bg='#2c3e50')
    progress_frame.pack(fill='x', pady=(0, 20))
    
    progress_label = tk.Label(
        progress_frame,
        text="جاري تحميل النظام...",
        font=('Arial', 10),
        fg='#ecf0f1',
        bg='#2c3e50'
    )
    progress_label.pack()
    
    # معلومات الإصدار
    version_label = tk.Label(
        content_frame,
        text="الإصدار 1.0 - 2024",
        font=('Arial', 8),
        fg='#95a5a6',
        bg='#2c3e50'
    )
    version_label.pack(side='bottom')
    
    # تحديث الشاشة
    splash.update()
    
    # انتظار لمدة 3 ثواني
    splash.after(3000, splash.destroy)
    splash.mainloop()

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    try:
        # عرض شاشة البداية
        show_splash_screen()
        
        # فحص المتطلبات
        if not check_requirements():
            return
        
        # إعداد بيئة العمل
        if not setup_environment():
            return
        
        # استيراد وتشغيل النظام الرئيسي
        try:
            from main_window import MainWindow
            
            # إنشاء وتشغيل النافذة الرئيسية
            app = MainWindow()
            app.run()
            
        except ImportError as e:
            messagebox.showerror(
                "خطأ في الاستيراد",
                f"فشل في استيراد النظام الرئيسي:\n{str(e)}\n\n"
                "تأكد من وجود جميع ملفات النظام في نفس المجلد"
            )
        except Exception as e:
            messagebox.showerror(
                "خطأ في تشغيل النظام",
                f"حدث خطأ أثناء تشغيل النظام:\n{str(e)}\n\n"
                "تفاصيل الخطأ:\n{traceback.format_exc()}"
            )
    
    except Exception as e:
        # خطأ عام في النظام
        try:
            messagebox.showerror(
                "خطأ عام",
                f"حدث خطأ غير متوقع:\n{str(e)}\n\n"
                "يرجى إعادة تشغيل النظام أو الاتصال بالدعم الفني"
            )
        except:
            # إذا فشل حتى messagebox، اطبع الخطأ في وحدة التحكم
            print(f"خطأ عام في النظام: {str(e)}")
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")

def show_help():
    """عرض نافذة المساعدة"""
    help_window = tk.Tk()
    help_window.title("مساعدة النظام")
    help_window.geometry("600x400")
    help_window.configure(bg='#f0f0f0')
    
    # النص
    help_text = """
    نظام التكاليف الصناعي المتكامل
    ================================
    
    نظام محاسبة صناعية متكامل لإدارة التكاليف والإنتاج
    
    المميزات الرئيسية:
    • إدارة البيانات الأساسية (المنتجات، الأجزاء، الورش، العمليات)
    • تسجيل بيانات الحركة (أوامر التشغيل، بطاقات التشغيل، صرف الخامات)
    • نظام استعلامات متقدم
    • تقارير شاملة
    • نظام نسخ احتياطي آمن
    
    متطلبات التشغيل:
    • Windows 7 أو أحدث (32 بت أو 64 بت)
    • Python 3.6 أو أحدث
    • 100 ميجابايت مساحة فارغة على القرص الصلب
    • 512 ميجابايت ذاكرة عشوائية
    
    للدعم الفني:
    يرجى الاتصال بفريق الدعم الفني
    
    حقوق الطبع والنشر © 2024
    جميع الحقوق محفوظة
    """
    
    text_widget = tk.Text(
        help_window,
        wrap='word',
        font=('Arial', 11),
        bg='white',
        fg='black',
        padx=20,
        pady=20
    )
    text_widget.pack(fill='both', expand=True, padx=10, pady=10)
    text_widget.insert('1.0', help_text)
    text_widget.config(state='disabled')
    
    # زر الإغلاق
    close_btn = tk.Button(
        help_window,
        text="إغلاق",
        command=help_window.destroy,
        font=('Arial', 12),
        width=10
    )
    close_btn.pack(pady=10)
    
    help_window.mainloop()

if __name__ == "__main__":
    # فحص المعاملات
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            show_help()
        elif sys.argv[1] == "--version" or sys.argv[1] == "-v":
            print("نظام التكاليف الصناعي المتكامل - الإصدار 1.0")
            print("Industrial Cost Management System - Version 1.0")
        else:
            print("معاملات غير صحيحة. استخدم --help للمساعدة")
    else:
        # تشغيل النظام العادي
        main()

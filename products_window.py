import tkinter as tk
from tkinter import ttk, messagebox
from database import DatabaseManager
from config import SystemConfig, UIConfig

class ProductsWindow:
    def __init__(self, parent=None):
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("إدارة المنتجات")
        self.window.geometry("1000x600")
        self.window.configure(bg=SystemConfig.BACKGROUND_COLOR)

        # الخطوط من الإعدادات
        self.arabic_font = SystemConfig.ARABIC_FONT
        self.title_font = SystemConfig.TITLE_FONT
        
        # قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغيرات النموذج
        self.selected_product_id = None
        self.setup_variables()
        
        self.create_widgets()
        self.setup_layout()
        self.load_products()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.name_var = tk.StringVar()
        self.code_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.unit_var = tk.StringVar()
        self.standard_cost_var = tk.DoubleVar()
        self.active_var = tk.BooleanVar(value=True)
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.window)
        
        # شريط العنوان
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="إدارة المنتجات",
            font=self.title_font
        )
        
        # إطار الإدخال
        self.input_frame = ttk.LabelFrame(self.main_frame, text="بيانات المنتج", padding=10)
        
        # حقول الإدخال
        ttk.Label(self.input_frame, text="اسم المنتج:", font=self.arabic_font).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.name_entry = ttk.Entry(self.input_frame, textvariable=self.name_var, font=self.arabic_font, width=30)
        self.name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="كود المنتج:", font=self.arabic_font).grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.code_entry = ttk.Entry(self.input_frame, textvariable=self.code_var, font=self.arabic_font, width=20)
        self.code_entry.grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الوصف:", font=self.arabic_font).grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.description_entry = ttk.Entry(self.input_frame, textvariable=self.description_var, font=self.arabic_font, width=30)
        self.description_entry.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الوحدة:", font=self.arabic_font).grid(row=1, column=2, sticky='e', padx=5, pady=5)
        self.unit_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.unit_var,
            values=SystemConfig.UNITS,
            font=self.arabic_font,
            width=17
        )
        self.unit_combo.grid(row=1, column=3, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="التكلفة المعيارية:", font=self.arabic_font).grid(row=2, column=0, sticky='e', padx=5, pady=5)
        self.cost_entry = ttk.Entry(self.input_frame, textvariable=self.standard_cost_var, font=self.arabic_font, width=30)
        self.cost_entry.grid(row=2, column=1, padx=5, pady=5)
        
        self.active_check = ttk.Checkbutton(
            self.input_frame,
            text="نشط",
            variable=self.active_var
        )
        self.active_check.grid(row=2, column=2, padx=5, pady=5)
        
        # إطار الأزرار
        self.buttons_frame = ttk.Frame(self.input_frame)
        self.buttons_frame.grid(row=3, column=0, columnspan=4, pady=20)
        
        self.add_btn = ttk.Button(
            self.buttons_frame,
            text="إضافة",
            command=self.add_product,
            width=12
        )
        self.add_btn.pack(side='left', padx=5)
        
        self.update_btn = ttk.Button(
            self.buttons_frame,
            text="تحديث",
            command=self.update_product,
            width=12,
            state='disabled'
        )
        self.update_btn.pack(side='left', padx=5)
        
        self.delete_btn = ttk.Button(
            self.buttons_frame,
            text="حذف",
            command=self.delete_product,
            width=12,
            state='disabled'
        )
        self.delete_btn.pack(side='left', padx=5)
        
        self.clear_btn = ttk.Button(
            self.buttons_frame,
            text="مسح",
            command=self.clear_form,
            width=12
        )
        self.clear_btn.pack(side='left', padx=5)
        
        # إطار قائمة المنتجات
        self.list_frame = ttk.LabelFrame(self.main_frame, text="قائمة المنتجات", padding=10)
        
        # جدول المنتجات
        columns = ('ID', 'الاسم', 'الكود', 'الوصف', 'الوحدة', 'التكلفة المعيارية', 'نشط', 'تاريخ الإنشاء')
        self.products_tree = ttk.Treeview(self.list_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.products_tree.heading(col, text=col)
            if col == 'ID':
                self.products_tree.column(col, width=50)
            elif col in ['الاسم', 'الوصف']:
                self.products_tree.column(col, width=150)
            elif col == 'الكود':
                self.products_tree.column(col, width=100)
            elif col == 'الوحدة':
                self.products_tree.column(col, width=80)
            elif col == 'التكلفة المعيارية':
                self.products_tree.column(col, width=120)
            elif col == 'نشط':
                self.products_tree.column(col, width=60)
            else:
                self.products_tree.column(col, width=120)
        
        # شريط التمرير
        self.scrollbar = ttk.Scrollbar(self.list_frame, orient='vertical', command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=self.scrollbar.set)
        
        # ربط الأحداث
        self.products_tree.bind('<<TreeviewSelect>>', self.on_product_select)
        
        # إطار البحث
        self.search_frame = ttk.LabelFrame(self.main_frame, text="البحث", padding=10)
        
        ttk.Label(self.search_frame, text="البحث:", font=self.arabic_font).pack(side='left', padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, font=self.arabic_font, width=30)
        self.search_entry.pack(side='left', padx=5)
        
        self.search_btn = ttk.Button(
            self.search_frame,
            text="بحث",
            command=self.search_products,
            width=10
        )
        self.search_btn.pack(side='left', padx=5)
        
        self.refresh_btn = ttk.Button(
            self.search_frame,
            text="تحديث",
            command=self.load_products,
            width=10
        )
        self.refresh_btn.pack(side='left', padx=5)
        
        # ربط البحث بالكتابة
        self.search_var.trace('w', self.on_search_change)
        
    def setup_layout(self):
        """ترتيب عناصر الواجهة"""
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.title_frame.pack(fill='x', pady=(0, 10))
        self.title_label.pack()
        
        self.input_frame.pack(fill='x', pady=(0, 10))
        
        self.search_frame.pack(fill='x', pady=(0, 10))
        
        self.list_frame.pack(fill='both', expand=True)
        self.products_tree.pack(side='left', fill='both', expand=True)
        self.scrollbar.pack(side='right', fill='y')
        
    def clear_form(self):
        """مسح النموذج"""
        self.name_var.set("")
        self.code_var.set("")
        self.description_var.set("")
        self.unit_var.set("")
        self.standard_cost_var.set(0.0)
        self.active_var.set(True)
        self.selected_product_id = None
        
        self.add_btn.config(state='normal')
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
        
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يجب إدخال اسم المنتج")
            return False
        
        if not self.unit_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار وحدة المنتج")
            return False
        
        try:
            cost = float(self.standard_cost_var.get())
            if cost < 0:
                messagebox.showerror("خطأ", "التكلفة المعيارية يجب أن تكون أكبر من أو تساوي صفر")
                return False
        except:
            messagebox.showerror("خطأ", "التكلفة المعيارية يجب أن تكون رقم صحيح")
            return False
        
        return True
        
    def add_product(self):
        """إضافة منتج جديد"""
        if not self.validate_form():
            return
        
        data = {
            'name': self.name_var.get().strip(),
            'code': self.code_var.get().strip() if self.code_var.get().strip() else None,
            'description': self.description_var.get().strip(),
            'unit': self.unit_var.get().strip(),
            'standard_cost': self.standard_cost_var.get(),
            'active': 1 if self.active_var.get() else 0
        }
        
        try:
            success = self.db.insert_record('products', data)
            if success:
                messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
                self.clear_form()
                self.load_products()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة المنتج")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def update_product(self):
        """تحديث منتج موجود"""
        if not self.selected_product_id:
            return
        
        if not self.validate_form():
            return
        
        data = {
            'name': self.name_var.get().strip(),
            'code': self.code_var.get().strip() if self.code_var.get().strip() else None,
            'description': self.description_var.get().strip(),
            'unit': self.unit_var.get().strip(),
            'standard_cost': self.standard_cost_var.get(),
            'active': 1 if self.active_var.get() else 0
        }
        
        try:
            success = self.db.update_record('products', data, 'id = ?', [self.selected_product_id])
            if success:
                messagebox.showinfo("نجح", "تم تحديث المنتج بنجاح")
                self.clear_form()
                self.load_products()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث المنتج")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def delete_product(self):
        """حذف منتج"""
        if not self.selected_product_id:
            return
        
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المنتج؟")
        if result:
            try:
                success = self.db.delete_record('products', 'id = ?', [self.selected_product_id])
                if success:
                    messagebox.showinfo("نجح", "تم حذف المنتج بنجاح")
                    self.clear_form()
                    self.load_products()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف المنتج")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def load_products(self):
        """تحميل قائمة المنتجات"""
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # جلب البيانات من قاعدة البيانات
        products = self.db.get_all_records('products', 'ORDER BY name')
        
        for product in products:
            active_text = "نعم" if product['active'] else "لا"
            created_date = product['created_date'][:10] if product['created_date'] else ""
            
            self.products_tree.insert('', 'end', values=(
                product['id'],
                product['name'],
                product['code'] or "",
                product['description'] or "",
                product['unit'] or "",
                f"{product['standard_cost']:.2f}",
                active_text,
                created_date
            ))
    
    def on_product_select(self, event):
        """عند اختيار منتج من القائمة"""
        selection = self.products_tree.selection()
        if selection:
            item = self.products_tree.item(selection[0])
            values = item['values']
            
            self.selected_product_id = values[0]
            self.name_var.set(values[1])
            self.code_var.set(values[2])
            self.description_var.set(values[3])
            self.unit_var.set(values[4])
            self.standard_cost_var.set(float(values[5]))
            self.active_var.set(values[6] == "نعم")
            
            self.add_btn.config(state='disabled')
            self.update_btn.config(state='normal')
            self.delete_btn.config(state='normal')
    
    def search_products(self):
        """البحث في المنتجات"""
        search_term = self.search_var.get().strip()
        if not search_term:
            self.load_products()
            return
        
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # البحث في قاعدة البيانات
        where_clause = "name LIKE ? OR code LIKE ? OR description LIKE ?"
        params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
        
        products = self.db.get_all_records('products', where_clause + " ORDER BY name", params)
        
        for product in products:
            active_text = "نعم" if product['active'] else "لا"
            created_date = product['created_date'][:10] if product['created_date'] else ""
            
            self.products_tree.insert('', 'end', values=(
                product['id'],
                product['name'],
                product['code'] or "",
                product['description'] or "",
                product['unit'] or "",
                f"{product['standard_cost']:.2f}",
                active_text,
                created_date
            ))
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        # البحث التلقائي عند الكتابة
        self.window.after(500, self.search_products)

if __name__ == "__main__":
    app = ProductsWindow()
    app.window.mainloop()

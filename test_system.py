#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار نظام التكاليف الصناعي المتكامل
Test file for Industrial Cost Management System
"""

import sys
import os

def test_imports():
    """اختبار استيراد جميع الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        # اختبار المكتبات الأساسية
        import tkinter as tk
        print("✅ tkinter - متوفر")
        
        import sqlite3
        print("✅ sqlite3 - متوفر")
        
        from datetime import datetime
        print("✅ datetime - متوفر")
        
        # اختبار وحدات النظام
        from config import SystemConfig, DatabaseConfig, UIConfig
        print("✅ config - متوفر")
        
        from database import DatabaseManager
        print("✅ database - متوفر")
        
        from main_window import MainWindow
        print("✅ main_window - متوفر")
        
        from products_window import ProductsWindow
        print("✅ products_window - متوفر")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database import DatabaseManager
        
        # إنشاء قاعدة البيانات
        db = DatabaseManager()
        print("✅ إنشاء قاعدة البيانات - نجح")
        
        # اختبار الاتصال
        if db.connect():
            print("✅ الاتصال بقاعدة البيانات - نجح")
            db.disconnect()
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
        
        # اختبار إدراج بيانات تجريبية
        test_product = {
            'name': 'منتج تجريبي',
            'code': 'TEST001',
            'description': 'منتج للاختبار',
            'unit': 'قطعة',
            'standard_cost': 100.0,
            'active': 1
        }
        
        if db.insert_record('products', test_product):
            print("✅ إدراج بيانات تجريبية - نجح")
            
            # اختبار جلب البيانات
            products = db.get_all_records('products')
            if products:
                print(f"✅ جلب البيانات - نجح ({len(products)} منتج)")
                
                # حذف البيانات التجريبية
                db.delete_record('products', 'code = ?', ['TEST001'])
                print("✅ حذف البيانات التجريبية - نجح")
            else:
                print("❌ فشل في جلب البيانات")
                return False
        else:
            print("❌ فشل في إدراج البيانات التجريبية")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_config():
    """اختبار ملف الإعدادات"""
    print("\n⚙️ اختبار ملف الإعدادات...")
    
    try:
        from config import SystemConfig, DatabaseConfig, UIConfig
        
        # اختبار إعدادات النظام
        print(f"✅ اسم النظام: {SystemConfig.SYSTEM_NAME}")
        print(f"✅ إصدار النظام: {SystemConfig.VERSION}")
        print(f"✅ قاعدة البيانات: {SystemConfig.DATABASE_NAME}")
        
        # اختبار إنشاء المجلدات
        backup_path = SystemConfig.get_backup_path()
        if os.path.exists(backup_path):
            print(f"✅ مجلد النسخ الاحتياطية: {backup_path}")
        
        reports_path = SystemConfig.get_reports_path()
        if os.path.exists(reports_path):
            print(f"✅ مجلد التقارير: {reports_path}")
        
        # اختبار إعدادات قاعدة البيانات
        pragma_settings = DatabaseConfig.get_pragma_settings()
        print(f"✅ إعدادات قاعدة البيانات: {len(pragma_settings)} إعداد")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def test_backup():
    """اختبار النسخ الاحتياطي"""
    print("\n💾 اختبار النسخ الاحتياطي...")
    
    try:
        from database import DatabaseManager
        
        db = DatabaseManager()
        
        # إنشاء نسخة احتياطية
        success, message = db.backup_database()
        if success:
            print(f"✅ إنشاء نسخة احتياطية - نجح")
            print(f"   {message}")
        else:
            print(f"❌ فشل في إنشاء نسخة احتياطية: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النسخ الاحتياطي: {e}")
        return False

def test_gui():
    """اختبار الواجهة الرسومية (بدون عرض)"""
    print("\n🖥️ اختبار الواجهة الرسومية...")

    try:
        import tkinter as tk

        # اختبار tkinter أولاً
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة

        # اختبار إنشاء عناصر أساسية
        test_label = tk.Label(root, text="اختبار")
        test_button = tk.Button(root, text="اختبار")

        print("✅ إنشاء عناصر tkinter أساسية - نجح")

        # اختبار استيراد النوافذ
        try:
            from main_window import MainWindow
            print("✅ استيراد النافذة الرئيسية - نجح")
        except Exception as e:
            print(f"❌ فشل في استيراد النافذة الرئيسية: {e}")
            return False

        try:
            from products_window import ProductsWindow
            print("✅ استيراد نافذة المنتجات - نجح")
        except Exception as e:
            print(f"❌ فشل في استيراد نافذة المنتجات: {e}")
            return False

        # إغلاق النافذة التجريبية
        root.destroy()

        print("✅ اختبار الواجهة الرسومية الأساسي - نجح")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرسومية: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نظام التكاليف الصناعي المتكامل")
    print("=" * 50)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("ملف الإعدادات", test_config),
        ("قاعدة البيانات", test_database),
        ("النسخ الاحتياطي", test_backup),
        ("الواجهة الرسومية", test_gui)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - نجح")
        else:
            print(f"❌ {test_name} - فشل")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

def show_system_info():
    """عرض معلومات النظام"""
    print("\n📋 معلومات النظام:")
    print("-" * 30)
    
    try:
        from config import SystemConfig
        info = SystemConfig.get_system_info()
        
        for key, value in info.items():
            print(f"{key}: {value}")
            
    except Exception as e:
        print(f"خطأ في جلب معلومات النظام: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--info":
            show_system_info()
        elif sys.argv[1] == "--quick":
            # اختبار سريع
            if test_imports() and test_config():
                print("✅ الاختبار السريع نجح")
            else:
                print("❌ الاختبار السريع فشل")
        else:
            print("الاستخدام:")
            print("  python test_system.py        # تشغيل جميع الاختبارات")
            print("  python test_system.py --info # عرض معلومات النظام")
            print("  python test_system.py --quick # اختبار سريع")
    else:
        # تشغيل جميع الاختبارات
        success = run_all_tests()
        sys.exit(0 if success else 1)

# نظام التكاليف الصناعي المتكامل
## Industrial Cost Management System

نظام محاسبة صناعية متكامل مبني بلغة Python لإدارة التكاليف والإنتاج، يعمل بسلاسة على جميع أنظمة Windows من الإصدار 7 فما فوق.

## 🎯 الهدف من النظام

نظام محاسبة صناعية مصمم لتسهيل إدارة المعلومات المالية والإنتاجية دون الحاجة لتعقيد أو أدوات خارجية، مع إمكانية حفظ واسترجاع البيانات في أي وقت.

## ✨ المميزات الرئيسية

### 🧩 المكونات الأساسية:
- **الواجهة الرئيسية**: شاشة تعرض اختيار السنة المالية مع أقسام متعددة
- **البيانات الأساسية**: إدارة المنتجات، الأجزاء، القطاعات، الورش، وعمليات التشغيل
- **بيانات الحركة**: تسجيل التكاليف الفعلية وتشغيل الأوامر
- **الاستعلام والتقارير**: استخراج تقارير شاملة عن الإنتاج والتكاليف
- **النسخ الاحتياطي**: نظام آمن لحفظ واسترجاع البيانات

### 💾 المميزات التقنية:
✅ واجهة بسيطة وسهلة الفهم برسوم مرئية للمستخدم  
✅ يدعم جميع نسخ Windows الحديثة والقديمة (7, 8, 10, 11 - 32/64bit)  
✅ قاعدة بيانات مستقرة (SQLite) لتفادي المشاكل  
✅ لا يحتاج اتصال دائم بالإنترنت  
✅ نظام نسخ احتياطي يدوي وآلي  
✅ إمكانية استيراد وتصدير البيانات  
✅ حماية دخول بكلمات مرور متعددة المستويات  

## 🔄 العلاقات بين الجداول

```
[المنتجات]
     ↓
[الأجزاء]
     ↓
[عمليات التشغيل]
     ↓
[الورش ← القطاعات]
     ↓
[المصاريف الصناعية]
```

## 📋 متطلبات التشغيل

- **نظام التشغيل**: Windows 7 أو أحدث (32 بت أو 64 بت)
- **Python**: الإصدار 3.6 أو أحدث
- **المساحة**: 100 ميجابايت مساحة فارغة على القرص الصلب
- **الذاكرة**: 512 ميجابايت ذاكرة عشوائية كحد أدنى
- **المكتبات المطلوبة**: 
  - tkinter (مدمجة مع Python)
  - sqlite3 (مدمجة مع Python)

## 🚀 كيفية التشغيل

### ⭐ الطريقة الأولى: التشغيل السريع (موصى به)
```bash
python START.py
```

### 🎨 الطريقة الثانية: النسخة المحدثة مباشرة
```bash
python run_updated.py
```

### 🎯 الطريقة الثالثة: نافذة الاختيار
```bash
python start_system.py
```

### ✨ الطريقة الرابعة: النظام المودرن فقط
```bash
python run_modern.py
```

### 🔄 الطريقة الخامسة: النظام الكلاسيكي فقط
```bash
python run_classic.py
```

### 📋 الطريقة السادسة: التشغيل الرئيسي
```bash
python main.py
```

> 💡 **نصيحة**: استخدم `python START.py` للحصول على أفضل تجربة مع النسخة المحدثة

## 📁 هيكل الملفات

```
نظام التكاليف الصناعي/
├── run_system.py          # ملف التشغيل الرئيسي
├── main_window.py         # النافذة الرئيسية
├── database.py            # إدارة قاعدة البيانات
├── products_window.py     # نافذة إدارة المنتجات
├── README.md              # ملف التوضيح
├── industrial_costs.db    # قاعدة البيانات (تُنشأ تلقائياً)
├── backups/               # مجلد النسخ الاحتياطية
├── reports/               # مجلد التقارير
├── exports/               # مجلد الملفات المُصدرة
└── logs/                  # مجلد ملفات السجلات
```

## ✨ المميزات الجديدة المضافة

### 🔗 ربط المنتجات بالأجزاء (BOM)
- **نافذة جديدة**: `product_parts_window.py`
- **الوظيفة**: ربط كل منتج بالأجزاء المطلوبة لتصنيعه
- **المميزات**:
  - تحديد الكمية المطلوبة من كل جزء
  - حساب تكلفة المنتج تلقائياً
  - وحدات قياس مختلفة
  - ملاحظات لكل علاقة

### 🎨 الواجهة المودرن
- **ملف الأنماط**: `modern_styles.py`
- **النافذة الرئيسية المودرن**: `modern_main_window.py`
- **المميزات**:
  - تصميم مودرن وعصري
  - ألوان متناسقة ومريحة للعين
  - شريط جانبي للتنقل
  - لوحة معلومات تفاعلية
  - تأثيرات بصرية احترافية
  - أيقونات حديثة

### 🚀 طرق التشغيل المتعددة
- **النظام المودرن**: `run_modern.py`
- **النظام الكلاسيكي**: `run_classic.py`
- **التشغيل الرئيسي**: `main.py`

## 🎮 كيفية الاستخدام

### 1. البدء:
- شغل النظام باستخدام `python run_system.py`
- ستظهر شاشة البداية ثم النافذة الرئيسية
- اختر السنة المالية من القائمة المنسدلة

### 2. البيانات الأساسية:
- انقر على "البيانات الأساسية"
- اختر "المنتجات" لإدارة المنتجات
- أدخل بيانات المنتج (الاسم، الكود، الوصف، الوحدة، التكلفة)
- استخدم أزرار "إضافة"، "تحديث"، "حذف" حسب الحاجة

### 3. البحث والتصفية:
- استخدم مربع البحث للعثور على منتجات معينة
- البحث يعمل في الاسم، الكود، والوصف

### 4. النسخ الاحتياطي:
- انقر على "نسخة احتياطية"
- اختر "إنشاء نسخة احتياطية" لحفظ البيانات
- اختر "استرجاع نسخة احتياطية" لاستعادة البيانات

## 🛠️ التطوير والتخصيص

### إضافة نوافذ جديدة:
1. أنشئ ملف جديد مثل `new_window.py`
2. استخدم نفس هيكل `products_window.py` كمرجع
3. أضف الاستيراد في `main_window.py`
4. أضف دالة جديدة لفتح النافذة

### تخصيص قاعدة البيانات:
- عدل ملف `database.py` لإضافة جداول جديدة
- استخدم دالة `create_tables()` لإنشاء الجداول
- استخدم الدوال المساعدة للتعامل مع البيانات

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

**1. خطأ "ModuleNotFoundError":**
```bash
# تأكد من تثبيت Python بشكل صحيح
python --version

# تأكد من وجود tkinter
python -c "import tkinter; print('tkinter متوفر')"
```

**2. خطأ في قاعدة البيانات:**
- احذف ملف `industrial_costs.db` وأعد تشغيل النظام
- سيتم إنشاء قاعدة بيانات جديدة تلقائياً

**3. مشاكل في الواجهة:**
- تأكد من دعم النظام للخطوط العربية
- جرب تشغيل النظام كمدير (Run as Administrator)

## 📞 الدعم الفني

للحصول على المساعدة:
- استخدم الأمر `python run_system.py --help`
- راجع ملفات السجلات في مجلد `logs/`
- تأكد من توفر جميع المتطلبات

## 📄 الترخيص

هذا النظام مطور لأغراض تعليمية وتجارية. جميع الحقوق محفوظة © 2024.

## 🔄 التحديثات المستقبلية

- [ ] إضافة نوافذ الأجزاء والورش
- [ ] تطوير نظام التقارير
- [ ] إضافة نظام المستخدمين والصلاحيات
- [ ] تطوير واجهة الاستعلامات
- [ ] إضافة الرسوم البيانية
- [ ] تطوير نظام الاستيراد والتصدير

---

**ملاحظة**: هذا النظام في مرحلة التطوير الأولى. المزيد من المميزات ستتم إضافتها تدريجياً.

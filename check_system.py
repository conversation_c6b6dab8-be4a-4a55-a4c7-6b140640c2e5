#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص النظام والتأكد من وجود جميع الملفات المطلوبة
System check to ensure all required files are present
"""

import os
import sys

def check_system():
    """فحص النظام والملفات المطلوبة"""
    
    print("🔍 فحص نظام التكاليف الصناعي المتكامل")
    print("=" * 50)
    
    # الملفات الأساسية المطلوبة
    required_files = {
        'ملفات التشغيل': [
            'START.py',
            'run_updated.py', 
            'start_system.py',
            'run_modern.py',
            'run_classic.py',
            'main.py'
        ],
        'ملفات النظام الأساسية': [
            'database.py',
            'config.py',
            'modern_styles.py'
        ],
        'النوافذ الرئيسية': [
            'main_window.py',
            'modern_main_window.py'
        ],
        'نوافذ البيانات الأساسية': [
            'products_window.py',
            'parts_window.py',
            'sectors_window.py',
            'workshops_window.py',
            'operations_window.py',
            'part_operations_window.py',
            'product_parts_window.py'
        ],
        'نوافذ بيانات الحركة': [
            'work_orders_window.py',
            'operation_cards_window.py',
            'material_issues_window.py',
            'defective_items_window.py'
        ],
        'نوافذ التقارير': [
            'queries_window.py',
            'reports_window.py'
        ]
    }
    
    total_files = 0
    existing_files = 0
    missing_files = []
    
    # فحص كل مجموعة ملفات
    for category, files in required_files.items():
        print(f"\n📁 {category}:")
        print("-" * 30)
        
        for file in files:
            total_files += 1
            if os.path.exists(file):
                print(f"  ✅ {file}")
                existing_files += 1
            else:
                print(f"  ❌ {file}")
                missing_files.append(file)
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("📊 نتيجة الفحص:")
    print("=" * 50)
    print(f"📁 إجمالي الملفات المطلوبة: {total_files}")
    print(f"✅ الملفات الموجودة: {existing_files}")
    print(f"❌ الملفات المفقودة: {len(missing_files)}")
    
    if missing_files:
        print(f"\n⚠️ الملفات المفقودة ({len(missing_files)}):")
        for file in missing_files:
            print(f"   • {file}")
        
        print(f"\n💡 نصائح:")
        print("   • تأكد من تحميل جميع ملفات النظام")
        print("   • تأكد من وضع جميع الملفات في نفس المجلد")
        print("   • راجع ملف FILES_LIST.md للحصول على قائمة كاملة")
        
        return False
    else:
        print("\n🎉 ممتاز! جميع الملفات المطلوبة موجودة")
        print("✅ النظام جاهز للتشغيل")
        
        print(f"\n🚀 طرق التشغيل الموصى بها:")
        print("   • python START.py (التشغيل السريع)")
        print("   • python run_updated.py (النسخة المحدثة)")
        print("   • python start_system.py (نافذة الاختيار)")
        
        return True

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص Python:")
    print("-" * 20)
    
    version = sys.version_info
    print(f"   الإصدار: Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 6:
        print("   ✅ إصدار Python مناسب")
        return True
    else:
        print("   ❌ إصدار Python قديم (يتطلب 3.6 أو أحدث)")
        return False

def check_modules():
    """فحص المكتبات المطلوبة"""
    print("\n📚 فحص المكتبات:")
    print("-" * 20)
    
    required_modules = ['tkinter', 'sqlite3', 'os', 'sys', 'datetime']
    all_available = True
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            all_available = False
    
    return all_available

def main():
    """الدالة الرئيسية"""
    print("🏭 نظام التكاليف الصناعي المتكامل - فحص النظام")
    print("=" * 60)
    
    # فحص Python
    python_ok = check_python_version()
    
    # فحص المكتبات
    modules_ok = check_modules()
    
    # فحص الملفات
    files_ok = check_system()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎯 النتيجة النهائية:")
    print("=" * 60)
    
    if python_ok and modules_ok and files_ok:
        print("🎉 النظام جاهز للتشغيل بالكامل!")
        print("\n🚀 لتشغيل النظام:")
        print("   python START.py")
        
        # محاولة تشغيل فحص سريع للاستيراد
        try:
            print("\n🧪 اختبار سريع للاستيراد...")
            from database import DatabaseManager
            from config import SystemConfig
            print("   ✅ استيراد الوحدات الأساسية نجح")
            
            try:
                from modern_main_window import ModernMainWindow
                print("   ✅ استيراد الواجهة المودرن نجح")
            except:
                print("   ⚠️ مشكلة في استيراد الواجهة المودرن")
                
        except Exception as e:
            print(f"   ❌ مشكلة في الاستيراد: {e}")
    else:
        print("❌ النظام غير جاهز للتشغيل")
        print("\nيرجى حل المشاكل المذكورة أعلاه قبل التشغيل")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from database import DatabaseManager
from config import SystemConfig, UIConfig

class DefectiveItemsWindow:
    def __init__(self, parent=None):
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("القطاعات المعيبة")
        self.window.geometry("1200x650")
        self.window.configure(bg=SystemConfig.BACKGROUND_COLOR)
        
        # الخطوط من الإعدادات
        self.arabic_font = SystemConfig.ARABIC_FONT
        self.title_font = SystemConfig.TITLE_FONT
        
        # قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغيرات النموذج
        self.selected_defect_id = None
        self.setup_variables()
        
        self.create_widgets()
        self.setup_layout()
        self.load_work_orders_combo()
        self.load_parts_combo()
        self.load_defective_items()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.work_order_var = tk.StringVar()
        self.part_var = tk.StringVar()
        self.quantity_var = tk.DoubleVar()
        self.defect_type_var = tk.StringVar()
        self.defect_reason_var = tk.StringVar()
        self.cost_impact_var = tk.DoubleVar()
        self.defect_date_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # تعيين القيم الافتراضية
        today = date.today().strftime("%Y-%m-%d")
        self.defect_date_var.set(today)
        self.defect_type_var.set("عيب في المواد")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.window)
        
        # شريط العنوان
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="القطاعات المعيبة",
            font=self.title_font
        )
        
        # إطار الإدخال
        self.input_frame = ttk.LabelFrame(self.main_frame, text="بيانات القطاع المعيب", padding=10)
        
        # الصف الأول
        ttk.Label(self.input_frame, text="أمر التشغيل:", font=self.arabic_font).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.work_order_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.work_order_var,
            font=self.arabic_font,
            width=30,
            state="readonly"
        )
        self.work_order_combo.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الجزء:", font=self.arabic_font).grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.part_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.part_var,
            font=self.arabic_font,
            width=25,
            state="readonly"
        )
        self.part_combo.grid(row=0, column=3, padx=5, pady=5)
        
        # الصف الثاني
        ttk.Label(self.input_frame, text="الكمية المعيبة:", font=self.arabic_font).grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.quantity_entry = ttk.Entry(self.input_frame, textvariable=self.quantity_var, font=self.arabic_font, width=30)
        self.quantity_entry.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="نوع العيب:", font=self.arabic_font).grid(row=1, column=2, sticky='e', padx=5, pady=5)
        self.defect_type_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.defect_type_var,
            values=SystemConfig.DEFECT_TYPES,
            font=self.arabic_font,
            width=22
        )
        self.defect_type_combo.grid(row=1, column=3, padx=5, pady=5)
        
        # الصف الثالث
        ttk.Label(self.input_frame, text="سبب العيب:", font=self.arabic_font).grid(row=2, column=0, sticky='e', padx=5, pady=5)
        self.defect_reason_entry = ttk.Entry(self.input_frame, textvariable=self.defect_reason_var, font=self.arabic_font, width=30)
        self.defect_reason_entry.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="تأثير التكلفة:", font=self.arabic_font).grid(row=2, column=2, sticky='e', padx=5, pady=5)
        self.cost_impact_entry = ttk.Entry(self.input_frame, textvariable=self.cost_impact_var, font=self.arabic_font, width=25)
        self.cost_impact_entry.grid(row=2, column=3, padx=5, pady=5)
        
        # الصف الرابع
        ttk.Label(self.input_frame, text="تاريخ اكتشاف العيب:", font=self.arabic_font).grid(row=3, column=0, sticky='e', padx=5, pady=5)
        self.defect_date_entry = ttk.Entry(self.input_frame, textvariable=self.defect_date_var, font=self.arabic_font, width=30)
        self.defect_date_entry.grid(row=3, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="ملاحظات:", font=self.arabic_font).grid(row=3, column=2, sticky='e', padx=5, pady=5)
        self.notes_entry = ttk.Entry(self.input_frame, textvariable=self.notes_var, font=self.arabic_font, width=25)
        self.notes_entry.grid(row=3, column=3, padx=5, pady=5)
        
        # إطار الأزرار
        self.buttons_frame = ttk.Frame(self.input_frame)
        self.buttons_frame.grid(row=4, column=0, columnspan=4, pady=20)
        
        self.add_btn = ttk.Button(
            self.buttons_frame,
            text="إضافة",
            command=self.add_defective_item,
            width=12
        )
        self.add_btn.pack(side='left', padx=5)
        
        self.update_btn = ttk.Button(
            self.buttons_frame,
            text="تحديث",
            command=self.update_defective_item,
            width=12,
            state='disabled'
        )
        self.update_btn.pack(side='left', padx=5)
        
        self.delete_btn = ttk.Button(
            self.buttons_frame,
            text="حذف",
            command=self.delete_defective_item,
            width=12,
            state='disabled'
        )
        self.delete_btn.pack(side='left', padx=5)
        
        self.clear_btn = ttk.Button(
            self.buttons_frame,
            text="مسح",
            command=self.clear_form,
            width=12
        )
        self.clear_btn.pack(side='left', padx=5)
        
        # إطار قائمة القطاعات المعيبة
        self.list_frame = ttk.LabelFrame(self.main_frame, text="قائمة القطاعات المعيبة", padding=10)
        
        # جدول القطاعات المعيبة
        columns = ('ID', 'أمر التشغيل', 'المنتج', 'الجزء', 'الكمية المعيبة', 'نوع العيب', 'سبب العيب', 'تأثير التكلفة', 'تاريخ العيب', 'ملاحظات')
        self.defects_tree = ttk.Treeview(self.list_frame, columns=columns, show='headings', height=12)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.defects_tree.heading(col, text=col)
            if col == 'ID':
                self.defects_tree.column(col, width=50)
            elif col in ['أمر التشغيل', 'المنتج', 'الجزء']:
                self.defects_tree.column(col, width=100)
            elif col in ['الكمية المعيبة', 'تأثير التكلفة']:
                self.defects_tree.column(col, width=100)
            elif col in ['نوع العيب', 'سبب العيب']:
                self.defects_tree.column(col, width=120)
            elif col == 'تاريخ العيب':
                self.defects_tree.column(col, width=100)
            elif col == 'ملاحظات':
                self.defects_tree.column(col, width=150)
        
        # شريط التمرير
        self.scrollbar = ttk.Scrollbar(self.list_frame, orient='vertical', command=self.defects_tree.yview)
        self.defects_tree.configure(yscrollcommand=self.scrollbar.set)
        
        # ربط الأحداث
        self.defects_tree.bind('<<TreeviewSelect>>', self.on_defect_select)
        
        # إطار البحث والإحصائيات
        self.search_frame = ttk.LabelFrame(self.main_frame, text="البحث والإحصائيات", padding=10)
        
        ttk.Label(self.search_frame, text="البحث:", font=self.arabic_font).pack(side='left', padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, font=self.arabic_font, width=20)
        self.search_entry.pack(side='left', padx=5)
        
        ttk.Label(self.search_frame, text="نوع العيب:", font=self.arabic_font).pack(side='left', padx=5)
        self.filter_defect_type_var = tk.StringVar()
        self.filter_defect_type_combo = ttk.Combobox(
            self.search_frame,
            textvariable=self.filter_defect_type_var,
            values=["الكل"] + SystemConfig.DEFECT_TYPES,
            font=self.arabic_font,
            width=15,
            state="readonly"
        )
        self.filter_defect_type_combo.set("الكل")
        self.filter_defect_type_combo.pack(side='left', padx=5)
        
        self.search_btn = ttk.Button(
            self.search_frame,
            text="بحث",
            command=self.search_defective_items,
            width=10
        )
        self.search_btn.pack(side='left', padx=5)
        
        self.refresh_btn = ttk.Button(
            self.search_frame,
            text="تحديث",
            command=self.load_defective_items,
            width=10
        )
        self.refresh_btn.pack(side='left', padx=5)
        
        self.stats_btn = ttk.Button(
            self.search_frame,
            text="إحصائيات",
            command=self.show_statistics,
            width=10
        )
        self.stats_btn.pack(side='left', padx=5)
        
        # ربط البحث بالكتابة
        self.search_var.trace('w', self.on_search_change)
        self.filter_defect_type_var.trace('w', self.on_filter_change)
        
    def setup_layout(self):
        """ترتيب عناصر الواجهة"""
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.title_frame.pack(fill='x', pady=(0, 10))
        self.title_label.pack()
        
        self.input_frame.pack(fill='x', pady=(0, 10))
        
        self.search_frame.pack(fill='x', pady=(0, 10))
        
        self.list_frame.pack(fill='both', expand=True)
        self.defects_tree.pack(side='left', fill='both', expand=True)
        self.scrollbar.pack(side='right', fill='y')
        
    def load_work_orders_combo(self):
        """تحميل قائمة أوامر التشغيل في الـ combobox"""
        query = """
        SELECT wo.id, wo.order_number, p.name as product_name 
        FROM work_orders wo 
        LEFT JOIN products p ON wo.product_id = p.id 
        ORDER BY wo.order_number
        """
        success, orders = self.db.execute_query(query)
        
        if success:
            order_values = [f"{order['id']} - {order['order_number']} ({order['product_name']})" for order in orders]
            self.work_order_combo['values'] = order_values
        
    def load_parts_combo(self):
        """تحميل قائمة الأجزاء في الـ combobox"""
        parts = self.db.get_all_records('parts', 'active = 1 ORDER BY name')
        part_values = [f"{part['id']} - {part['name']}" for part in parts]
        self.part_combo['values'] = part_values
        
    def clear_form(self):
        """مسح النموذج"""
        self.work_order_var.set("")
        self.part_var.set("")
        self.quantity_var.set(0.0)
        self.defect_type_var.set("عيب في المواد")
        self.defect_reason_var.set("")
        self.cost_impact_var.set(0.0)
        today = date.today().strftime("%Y-%m-%d")
        self.defect_date_var.set(today)
        self.notes_var.set("")
        self.selected_defect_id = None
        
        self.add_btn.config(state='normal')
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
        
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.work_order_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار أمر التشغيل")
            return False
        
        if not self.part_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار الجزء")
            return False
        
        try:
            quantity = float(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية المعيبة يجب أن تكون أكبر من صفر")
                return False
        except:
            messagebox.showerror("خطأ", "الكمية المعيبة يجب أن تكون رقم صحيح")
            return False
        
        try:
            cost_impact = float(self.cost_impact_var.get())
            if cost_impact < 0:
                messagebox.showerror("خطأ", "تأثير التكلفة يجب أن يكون أكبر من أو يساوي صفر")
                return False
        except:
            messagebox.showerror("خطأ", "تأثير التكلفة يجب أن يكون رقم صحيح")
            return False
        
        # التحقق من صحة التاريخ
        try:
            datetime.strptime(self.defect_date_var.get(), "%Y-%m-%d")
        except:
            messagebox.showerror("خطأ", "تنسيق التاريخ يجب أن يكون YYYY-MM-DD")
            return False
        
        return True
        
    def get_id_from_combo(self, combo_text):
        """استخراج المعرف من النص المختار"""
        if combo_text:
            return int(combo_text.split(' - ')[0])
        return None
        
    def add_defective_item(self):
        """إضافة قطاع معيب جديد"""
        if not self.validate_form():
            return
        
        work_order_id = self.get_id_from_combo(self.work_order_var.get())
        part_id = self.get_id_from_combo(self.part_var.get())
        
        if not all([work_order_id, part_id]):
            messagebox.showerror("خطأ", "يجب اختيار أمر التشغيل والجزء")
            return
        
        data = {
            'work_order_id': work_order_id,
            'part_id': part_id,
            'quantity': self.quantity_var.get(),
            'defect_type': self.defect_type_var.get(),
            'defect_reason': self.defect_reason_var.get().strip(),
            'cost_impact': self.cost_impact_var.get(),
            'defect_date': self.defect_date_var.get(),
            'notes': self.notes_var.get().strip()
        }
        
        try:
            success = self.db.insert_record('defective_items', data)
            if success:
                messagebox.showinfo("نجح", "تم إضافة القطاع المعيب بنجاح")
                self.clear_form()
                self.load_defective_items()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة القطاع المعيب")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def update_defective_item(self):
        """تحديث قطاع معيب موجود"""
        if not self.selected_defect_id:
            return
        
        if not self.validate_form():
            return
        
        work_order_id = self.get_id_from_combo(self.work_order_var.get())
        part_id = self.get_id_from_combo(self.part_var.get())
        
        if not all([work_order_id, part_id]):
            messagebox.showerror("خطأ", "يجب اختيار أمر التشغيل والجزء")
            return
        
        data = {
            'work_order_id': work_order_id,
            'part_id': part_id,
            'quantity': self.quantity_var.get(),
            'defect_type': self.defect_type_var.get(),
            'defect_reason': self.defect_reason_var.get().strip(),
            'cost_impact': self.cost_impact_var.get(),
            'defect_date': self.defect_date_var.get(),
            'notes': self.notes_var.get().strip()
        }
        
        try:
            success = self.db.update_record('defective_items', data, 'id = ?', [self.selected_defect_id])
            if success:
                messagebox.showinfo("نجح", "تم تحديث القطاع المعيب بنجاح")
                self.clear_form()
                self.load_defective_items()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث القطاع المعيب")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def delete_defective_item(self):
        """حذف قطاع معيب"""
        if not self.selected_defect_id:
            return
        
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف القطاع المعيب؟")
        if result:
            try:
                success = self.db.delete_record('defective_items', 'id = ?', [self.selected_defect_id])
                if success:
                    messagebox.showinfo("نجح", "تم حذف القطاع المعيب بنجاح")
                    self.clear_form()
                    self.load_defective_items()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف القطاع المعيب")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def load_defective_items(self):
        """تحميل قائمة القطاعات المعيبة"""
        # مسح البيانات الحالية
        for item in self.defects_tree.get_children():
            self.defects_tree.delete(item)
        
        # جلب البيانات من قاعدة البيانات مع ربط الجداول
        query = """
        SELECT di.*, wo.order_number, p.name as product_name, pt.name as part_name 
        FROM defective_items di 
        LEFT JOIN work_orders wo ON di.work_order_id = wo.id 
        LEFT JOIN products p ON wo.product_id = p.id 
        LEFT JOIN parts pt ON di.part_id = pt.id 
        ORDER BY di.defect_date DESC, di.created_date DESC
        """
        success, defects = self.db.execute_query(query)
        
        if success:
            for defect in defects:
                self.defects_tree.insert('', 'end', values=(
                    defect['id'],
                    defect['order_number'] or "",
                    defect['product_name'] or "",
                    defect['part_name'] or "",
                    f"{defect['quantity']:.2f}",
                    defect['defect_type'],
                    defect['defect_reason'] or "",
                    f"{defect['cost_impact']:.2f}",
                    defect['defect_date'] or "",
                    defect['notes'] or ""
                ))
    
    def on_defect_select(self, event):
        """عند اختيار قطاع معيب من القائمة"""
        selection = self.defects_tree.selection()
        if selection:
            item = self.defects_tree.item(selection[0])
            values = item['values']
            
            self.selected_defect_id = values[0]
            
            # البحث عن القيم المناسبة في الـ comboboxes
            order_number = values[1]
            for order_option in self.work_order_combo['values']:
                if order_number in order_option:
                    self.work_order_var.set(order_option)
                    break
            
            part_name = values[3]
            for part_option in self.part_combo['values']:
                if part_name in part_option:
                    self.part_var.set(part_option)
                    break
            
            self.quantity_var.set(float(values[4]))
            self.defect_type_var.set(values[5])
            self.defect_reason_var.set(values[6])
            self.cost_impact_var.set(float(values[7]))
            self.defect_date_var.set(values[8])
            self.notes_var.set(values[9])
            
            self.add_btn.config(state='disabled')
            self.update_btn.config(state='normal')
            self.delete_btn.config(state='normal')
    
    def search_defective_items(self):
        """البحث في القطاعات المعيبة"""
        search_term = self.search_var.get().strip()
        defect_type_filter = self.filter_defect_type_var.get()
        
        # مسح البيانات الحالية
        for item in self.defects_tree.get_children():
            self.defects_tree.delete(item)
        
        # بناء الاستعلام
        query = """
        SELECT di.*, wo.order_number, p.name as product_name, pt.name as part_name 
        FROM defective_items di 
        LEFT JOIN work_orders wo ON di.work_order_id = wo.id 
        LEFT JOIN products p ON wo.product_id = p.id 
        LEFT JOIN parts pt ON di.part_id = pt.id 
        WHERE 1=1
        """
        params = []
        
        if search_term:
            query += " AND (wo.order_number LIKE ? OR p.name LIKE ? OR pt.name LIKE ? OR di.defect_reason LIKE ?)"
            params.extend([f"%{search_term}%"] * 4)
        
        if defect_type_filter and defect_type_filter != "الكل":
            query += " AND di.defect_type = ?"
            params.append(defect_type_filter)
        
        query += " ORDER BY di.defect_date DESC, di.created_date DESC"
        
        success, defects = self.db.execute_query(query, params)
        
        if success:
            for defect in defects:
                self.defects_tree.insert('', 'end', values=(
                    defect['id'],
                    defect['order_number'] or "",
                    defect['product_name'] or "",
                    defect['part_name'] or "",
                    f"{defect['quantity']:.2f}",
                    defect['defect_type'],
                    defect['defect_reason'] or "",
                    f"{defect['cost_impact']:.2f}",
                    defect['defect_date'] or "",
                    defect['notes'] or ""
                ))
    
    def show_statistics(self):
        """عرض إحصائيات العيوب"""
        # حساب إحصائيات العيوب
        query = """
        SELECT 
            defect_type,
            COUNT(*) as count,
            SUM(quantity) as total_quantity,
            SUM(cost_impact) as total_cost_impact
        FROM defective_items 
        GROUP BY defect_type
        ORDER BY total_cost_impact DESC
        """
        success, stats = self.db.execute_query(query)
        
        if success:
            stats_text = "إحصائيات العيوب:\n" + "="*50 + "\n\n"
            total_items = 0
            total_quantity = 0
            total_cost = 0
            
            for stat in stats:
                stats_text += f"نوع العيب: {stat['defect_type']}\n"
                stats_text += f"عدد الحالات: {stat['count']}\n"
                stats_text += f"إجمالي الكمية: {stat['total_quantity']:.2f}\n"
                stats_text += f"إجمالي تأثير التكلفة: {stat['total_cost_impact']:.2f}\n"
                stats_text += "-" * 30 + "\n"
                
                total_items += stat['count']
                total_quantity += stat['total_quantity']
                total_cost += stat['total_cost_impact']
            
            stats_text += f"\nالإجمالي العام:\n"
            stats_text += f"إجمالي الحالات: {total_items}\n"
            stats_text += f"إجمالي الكمية المعيبة: {total_quantity:.2f}\n"
            stats_text += f"إجمالي تأثير التكلفة: {total_cost:.2f}\n"
            
            # إنشاء نافذة الإحصائيات
            stats_window = tk.Toplevel(self.window)
            stats_window.title("إحصائيات العيوب")
            stats_window.geometry("500x400")
            
            text_widget = tk.Text(stats_window, wrap='word', font=self.arabic_font)
            text_widget.pack(fill='both', expand=True, padx=10, pady=10)
            text_widget.insert('1.0', stats_text)
            text_widget.config(state='disabled')
        else:
            messagebox.showerror("خطأ", "فشل في جلب الإحصائيات")
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        self.window.after(500, self.search_defective_items)
    
    def on_filter_change(self, *args):
        """عند تغيير فلتر نوع العيب"""
        self.search_defective_items()

if __name__ == "__main__":
    app = DefectiveItemsWindow()
    app.window.mainloop()

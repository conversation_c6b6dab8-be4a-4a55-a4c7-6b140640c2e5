import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from database import DatabaseManager
from config import SystemConfig, UIConfig

class OperationCardsWindow:
    def __init__(self, parent=None):
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.window.title("بطاقات التشغيل")
        self.window.geometry("1300x700")
        self.window.configure(bg=SystemConfig.BACKGROUND_COLOR)
        
        # الخطوط من الإعدادات
        self.arabic_font = SystemConfig.ARABIC_FONT
        self.title_font = SystemConfig.TITLE_FONT
        
        # قاعدة البيانات
        self.db = DatabaseManager()
        
        # متغيرات النموذج
        self.selected_card_id = None
        self.setup_variables()
        
        self.create_widgets()
        self.setup_layout()
        self.load_work_orders_combo()
        self.load_parts_combo()
        self.load_operations_combo()
        self.load_operation_cards()
        
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.work_order_var = tk.StringVar()
        self.part_var = tk.StringVar()
        self.operation_var = tk.StringVar()
        self.actual_time_var = tk.DoubleVar()
        self.actual_cost_var = tk.DoubleVar()
        self.operator_name_var = tk.StringVar()
        self.operation_date_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # تعيين القيم الافتراضية
        today = date.today().strftime("%Y-%m-%d")
        self.operation_date_var.set(today)
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.window)
        
        # شريط العنوان
        self.title_frame = ttk.Frame(self.main_frame)
        self.title_label = ttk.Label(
            self.title_frame,
            text="بطاقات التشغيل",
            font=self.title_font
        )
        
        # إطار الإدخال
        self.input_frame = ttk.LabelFrame(self.main_frame, text="بيانات بطاقة التشغيل", padding=10)
        
        # الصف الأول
        ttk.Label(self.input_frame, text="أمر التشغيل:", font=self.arabic_font).grid(row=0, column=0, sticky='e', padx=5, pady=5)
        self.work_order_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.work_order_var,
            font=self.arabic_font,
            width=30,
            state="readonly"
        )
        self.work_order_combo.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الجزء:", font=self.arabic_font).grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.part_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.part_var,
            font=self.arabic_font,
            width=25,
            state="readonly"
        )
        self.part_combo.grid(row=0, column=3, padx=5, pady=5)
        
        # الصف الثاني
        ttk.Label(self.input_frame, text="العملية:", font=self.arabic_font).grid(row=1, column=0, sticky='e', padx=5, pady=5)
        self.operation_combo = ttk.Combobox(
            self.input_frame,
            textvariable=self.operation_var,
            font=self.arabic_font,
            width=30,
            state="readonly"
        )
        self.operation_combo.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="الوقت الفعلي (ساعة):", font=self.arabic_font).grid(row=1, column=2, sticky='e', padx=5, pady=5)
        self.actual_time_entry = ttk.Entry(self.input_frame, textvariable=self.actual_time_var, font=self.arabic_font, width=25)
        self.actual_time_entry.grid(row=1, column=3, padx=5, pady=5)
        
        # الصف الثالث
        ttk.Label(self.input_frame, text="التكلفة الفعلية:", font=self.arabic_font).grid(row=2, column=0, sticky='e', padx=5, pady=5)
        self.actual_cost_entry = ttk.Entry(self.input_frame, textvariable=self.actual_cost_var, font=self.arabic_font, width=30)
        self.actual_cost_entry.grid(row=2, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="اسم المشغل:", font=self.arabic_font).grid(row=2, column=2, sticky='e', padx=5, pady=5)
        self.operator_entry = ttk.Entry(self.input_frame, textvariable=self.operator_name_var, font=self.arabic_font, width=25)
        self.operator_entry.grid(row=2, column=3, padx=5, pady=5)
        
        # الصف الرابع
        ttk.Label(self.input_frame, text="تاريخ العملية:", font=self.arabic_font).grid(row=3, column=0, sticky='e', padx=5, pady=5)
        self.operation_date_entry = ttk.Entry(self.input_frame, textvariable=self.operation_date_var, font=self.arabic_font, width=30)
        self.operation_date_entry.grid(row=3, column=1, padx=5, pady=5)
        
        ttk.Label(self.input_frame, text="ملاحظات:", font=self.arabic_font).grid(row=3, column=2, sticky='e', padx=5, pady=5)
        self.notes_entry = ttk.Entry(self.input_frame, textvariable=self.notes_var, font=self.arabic_font, width=25)
        self.notes_entry.grid(row=3, column=3, padx=5, pady=5)
        
        # إطار الأزرار
        self.buttons_frame = ttk.Frame(self.input_frame)
        self.buttons_frame.grid(row=4, column=0, columnspan=4, pady=20)
        
        self.add_btn = ttk.Button(
            self.buttons_frame,
            text="إضافة",
            command=self.add_operation_card,
            width=12
        )
        self.add_btn.pack(side='left', padx=5)
        
        self.update_btn = ttk.Button(
            self.buttons_frame,
            text="تحديث",
            command=self.update_operation_card,
            width=12,
            state='disabled'
        )
        self.update_btn.pack(side='left', padx=5)
        
        self.delete_btn = ttk.Button(
            self.buttons_frame,
            text="حذف",
            command=self.delete_operation_card,
            width=12,
            state='disabled'
        )
        self.delete_btn.pack(side='left', padx=5)
        
        self.clear_btn = ttk.Button(
            self.buttons_frame,
            text="مسح",
            command=self.clear_form,
            width=12
        )
        self.clear_btn.pack(side='left', padx=5)
        
        self.calculate_btn = ttk.Button(
            self.buttons_frame,
            text="حساب التكلفة",
            command=self.calculate_cost,
            width=12
        )
        self.calculate_btn.pack(side='left', padx=5)
        
        # إطار قائمة بطاقات التشغيل
        self.list_frame = ttk.LabelFrame(self.main_frame, text="قائمة بطاقات التشغيل", padding=10)
        
        # جدول بطاقات التشغيل
        columns = ('ID', 'أمر التشغيل', 'المنتج', 'الجزء', 'العملية', 'الوقت الفعلي', 'التكلفة الفعلية', 'المشغل', 'تاريخ العملية', 'ملاحظات')
        self.cards_tree = ttk.Treeview(self.list_frame, columns=columns, show='headings', height=12)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.cards_tree.heading(col, text=col)
            if col == 'ID':
                self.cards_tree.column(col, width=50)
            elif col in ['أمر التشغيل', 'المنتج', 'الجزء', 'العملية']:
                self.cards_tree.column(col, width=120)
            elif col in ['الوقت الفعلي', 'التكلفة الفعلية']:
                self.cards_tree.column(col, width=100)
            elif col in ['المشغل', 'تاريخ العملية']:
                self.cards_tree.column(col, width=100)
            elif col == 'ملاحظات':
                self.cards_tree.column(col, width=150)
        
        # شريط التمرير
        self.scrollbar = ttk.Scrollbar(self.list_frame, orient='vertical', command=self.cards_tree.yview)
        self.cards_tree.configure(yscrollcommand=self.scrollbar.set)
        
        # ربط الأحداث
        self.cards_tree.bind('<<TreeviewSelect>>', self.on_card_select)
        
        # إطار البحث
        self.search_frame = ttk.LabelFrame(self.main_frame, text="البحث والتصفية", padding=10)
        
        ttk.Label(self.search_frame, text="البحث:", font=self.arabic_font).pack(side='left', padx=5)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, font=self.arabic_font, width=25)
        self.search_entry.pack(side='left', padx=5)
        
        self.search_btn = ttk.Button(
            self.search_frame,
            text="بحث",
            command=self.search_operation_cards,
            width=10
        )
        self.search_btn.pack(side='left', padx=5)
        
        self.refresh_btn = ttk.Button(
            self.search_frame,
            text="تحديث",
            command=self.load_operation_cards,
            width=10
        )
        self.refresh_btn.pack(side='left', padx=5)
        
        # ربط البحث بالكتابة
        self.search_var.trace('w', self.on_search_change)
        
    def setup_layout(self):
        """ترتيب عناصر الواجهة"""
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.title_frame.pack(fill='x', pady=(0, 10))
        self.title_label.pack()
        
        self.input_frame.pack(fill='x', pady=(0, 10))
        
        self.search_frame.pack(fill='x', pady=(0, 10))
        
        self.list_frame.pack(fill='both', expand=True)
        self.cards_tree.pack(side='left', fill='both', expand=True)
        self.scrollbar.pack(side='right', fill='y')
        
    def load_work_orders_combo(self):
        """تحميل قائمة أوامر التشغيل في الـ combobox"""
        query = """
        SELECT wo.id, wo.order_number, p.name as product_name 
        FROM work_orders wo 
        LEFT JOIN products p ON wo.product_id = p.id 
        WHERE wo.status IN ('جديد', 'قيد التنفيذ')
        ORDER BY wo.order_number
        """
        success, orders = self.db.execute_query(query)
        
        if success:
            order_values = [f"{order['id']} - {order['order_number']} ({order['product_name']})" for order in orders]
            self.work_order_combo['values'] = order_values
        
    def load_parts_combo(self):
        """تحميل قائمة الأجزاء في الـ combobox"""
        parts = self.db.get_all_records('parts', 'active = 1 ORDER BY name')
        part_values = [f"{part['id']} - {part['name']}" for part in parts]
        self.part_combo['values'] = part_values
        
    def load_operations_combo(self):
        """تحميل قائمة العمليات في الـ combobox"""
        query = """
        SELECT o.id, o.name, w.name as workshop_name 
        FROM operations o 
        LEFT JOIN workshops w ON o.workshop_id = w.id 
        WHERE o.active = 1 
        ORDER BY o.name
        """
        success, operations = self.db.execute_query(query)
        
        if success:
            operation_values = [f"{op['id']} - {op['name']} ({op['workshop_name']})" for op in operations]
            self.operation_combo['values'] = operation_values
        
    def clear_form(self):
        """مسح النموذج"""
        self.work_order_var.set("")
        self.part_var.set("")
        self.operation_var.set("")
        self.actual_time_var.set(0.0)
        self.actual_cost_var.set(0.0)
        self.operator_name_var.set("")
        today = date.today().strftime("%Y-%m-%d")
        self.operation_date_var.set(today)
        self.notes_var.set("")
        self.selected_card_id = None
        
        self.add_btn.config(state='normal')
        self.update_btn.config(state='disabled')
        self.delete_btn.config(state='disabled')
        
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.work_order_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار أمر التشغيل")
            return False
        
        if not self.part_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار الجزء")
            return False
        
        if not self.operation_var.get().strip():
            messagebox.showerror("خطأ", "يجب اختيار العملية")
            return False
        
        try:
            time = float(self.actual_time_var.get())
            if time < 0:
                messagebox.showerror("خطأ", "الوقت الفعلي يجب أن يكون أكبر من أو يساوي صفر")
                return False
        except:
            messagebox.showerror("خطأ", "الوقت الفعلي يجب أن يكون رقم صحيح")
            return False
        
        try:
            cost = float(self.actual_cost_var.get())
            if cost < 0:
                messagebox.showerror("خطأ", "التكلفة الفعلية يجب أن تكون أكبر من أو تساوي صفر")
                return False
        except:
            messagebox.showerror("خطأ", "التكلفة الفعلية يجب أن تكون رقم صحيح")
            return False
        
        # التحقق من صحة التاريخ
        try:
            datetime.strptime(self.operation_date_var.get(), "%Y-%m-%d")
        except:
            messagebox.showerror("خطأ", "تنسيق التاريخ يجب أن يكون YYYY-MM-DD")
            return False
        
        return True
        
    def get_id_from_combo(self, combo_text):
        """استخراج المعرف من النص المختار"""
        if combo_text:
            return int(combo_text.split(' - ')[0])
        return None
        
    def calculate_cost(self):
        """حساب التكلفة بناءً على الوقت الفعلي والعملية"""
        operation_id = self.get_id_from_combo(self.operation_var.get())
        if not operation_id:
            messagebox.showerror("خطأ", "يجب اختيار العملية أولاً")
            return
        
        try:
            actual_time = float(self.actual_time_var.get())
            if actual_time <= 0:
                messagebox.showerror("خطأ", "يجب إدخال الوقت الفعلي أولاً")
                return
        except:
            messagebox.showerror("خطأ", "الوقت الفعلي يجب أن يكون رقم صحيح")
            return
        
        # جلب تكلفة العملية بالساعة
        operation = self.db.get_all_records('operations', 'id = ?', [operation_id])
        if operation:
            cost_per_hour = operation[0]['cost_per_hour']
            calculated_cost = actual_time * cost_per_hour
            self.actual_cost_var.set(calculated_cost)
            messagebox.showinfo("تم الحساب", f"التكلفة المحسوبة: {calculated_cost:.2f}")
        else:
            messagebox.showerror("خطأ", "لم يتم العثور على بيانات العملية")
        
    def add_operation_card(self):
        """إضافة بطاقة تشغيل جديدة"""
        if not self.validate_form():
            return
        
        work_order_id = self.get_id_from_combo(self.work_order_var.get())
        part_id = self.get_id_from_combo(self.part_var.get())
        operation_id = self.get_id_from_combo(self.operation_var.get())
        
        if not all([work_order_id, part_id, operation_id]):
            messagebox.showerror("خطأ", "يجب اختيار جميع الحقول المطلوبة")
            return
        
        data = {
            'work_order_id': work_order_id,
            'part_id': part_id,
            'operation_id': operation_id,
            'actual_time': self.actual_time_var.get(),
            'actual_cost': self.actual_cost_var.get(),
            'operator_name': self.operator_name_var.get().strip(),
            'operation_date': self.operation_date_var.get(),
            'notes': self.notes_var.get().strip()
        }
        
        try:
            success = self.db.insert_record('operation_cards', data)
            if success:
                messagebox.showinfo("نجح", "تم إضافة بطاقة التشغيل بنجاح")
                self.clear_form()
                self.load_operation_cards()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة بطاقة التشغيل")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def update_operation_card(self):
        """تحديث بطاقة تشغيل موجودة"""
        if not self.selected_card_id:
            return
        
        if not self.validate_form():
            return
        
        work_order_id = self.get_id_from_combo(self.work_order_var.get())
        part_id = self.get_id_from_combo(self.part_var.get())
        operation_id = self.get_id_from_combo(self.operation_var.get())
        
        if not all([work_order_id, part_id, operation_id]):
            messagebox.showerror("خطأ", "يجب اختيار جميع الحقول المطلوبة")
            return
        
        data = {
            'work_order_id': work_order_id,
            'part_id': part_id,
            'operation_id': operation_id,
            'actual_time': self.actual_time_var.get(),
            'actual_cost': self.actual_cost_var.get(),
            'operator_name': self.operator_name_var.get().strip(),
            'operation_date': self.operation_date_var.get(),
            'notes': self.notes_var.get().strip()
        }
        
        try:
            success = self.db.update_record('operation_cards', data, 'id = ?', [self.selected_card_id])
            if success:
                messagebox.showinfo("نجح", "تم تحديث بطاقة التشغيل بنجاح")
                self.clear_form()
                self.load_operation_cards()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث بطاقة التشغيل")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def delete_operation_card(self):
        """حذف بطاقة تشغيل"""
        if not self.selected_card_id:
            return
        
        result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف بطاقة التشغيل؟")
        if result:
            try:
                success = self.db.delete_record('operation_cards', 'id = ?', [self.selected_card_id])
                if success:
                    messagebox.showinfo("نجح", "تم حذف بطاقة التشغيل بنجاح")
                    self.clear_form()
                    self.load_operation_cards()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف بطاقة التشغيل")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")
    
    def load_operation_cards(self):
        """تحميل قائمة بطاقات التشغيل"""
        # مسح البيانات الحالية
        for item in self.cards_tree.get_children():
            self.cards_tree.delete(item)
        
        # جلب البيانات من قاعدة البيانات مع ربط الجداول
        query = """
        SELECT oc.*, wo.order_number, p.name as product_name, 
               pt.name as part_name, o.name as operation_name 
        FROM operation_cards oc 
        LEFT JOIN work_orders wo ON oc.work_order_id = wo.id 
        LEFT JOIN products p ON wo.product_id = p.id 
        LEFT JOIN parts pt ON oc.part_id = pt.id 
        LEFT JOIN operations o ON oc.operation_id = o.id 
        ORDER BY oc.operation_date DESC, oc.created_date DESC
        """
        success, cards = self.db.execute_query(query)
        
        if success:
            for card in cards:
                self.cards_tree.insert('', 'end', values=(
                    card['id'],
                    card['order_number'] or "",
                    card['product_name'] or "",
                    card['part_name'] or "",
                    card['operation_name'] or "",
                    f"{card['actual_time']:.2f}",
                    f"{card['actual_cost']:.2f}",
                    card['operator_name'] or "",
                    card['operation_date'] or "",
                    card['notes'] or ""
                ))
    
    def on_card_select(self, event):
        """عند اختيار بطاقة من القائمة"""
        selection = self.cards_tree.selection()
        if selection:
            item = self.cards_tree.item(selection[0])
            values = item['values']
            
            self.selected_card_id = values[0]
            
            # البحث عن القيم المناسبة في الـ comboboxes
            order_number = values[1]
            for order_option in self.work_order_combo['values']:
                if order_number in order_option:
                    self.work_order_var.set(order_option)
                    break
            
            part_name = values[3]
            for part_option in self.part_combo['values']:
                if part_name in part_option:
                    self.part_var.set(part_option)
                    break
            
            operation_name = values[4]
            for operation_option in self.operation_combo['values']:
                if operation_name in operation_option:
                    self.operation_var.set(operation_option)
                    break
            
            self.actual_time_var.set(float(values[5]))
            self.actual_cost_var.set(float(values[6]))
            self.operator_name_var.set(values[7])
            self.operation_date_var.set(values[8])
            self.notes_var.set(values[9])
            
            self.add_btn.config(state='disabled')
            self.update_btn.config(state='normal')
            self.delete_btn.config(state='normal')
    
    def search_operation_cards(self):
        """البحث في بطاقات التشغيل"""
        search_term = self.search_var.get().strip()
        if not search_term:
            self.load_operation_cards()
            return
        
        # مسح البيانات الحالية
        for item in self.cards_tree.get_children():
            self.cards_tree.delete(item)
        
        # البحث في قاعدة البيانات
        query = """
        SELECT oc.*, wo.order_number, p.name as product_name, 
               pt.name as part_name, o.name as operation_name 
        FROM operation_cards oc 
        LEFT JOIN work_orders wo ON oc.work_order_id = wo.id 
        LEFT JOIN products p ON wo.product_id = p.id 
        LEFT JOIN parts pt ON oc.part_id = pt.id 
        LEFT JOIN operations o ON oc.operation_id = o.id 
        WHERE wo.order_number LIKE ? OR p.name LIKE ? OR pt.name LIKE ? 
           OR o.name LIKE ? OR oc.operator_name LIKE ?
        ORDER BY oc.operation_date DESC, oc.created_date DESC
        """
        params = [f"%{search_term}%"] * 5
        
        success, cards = self.db.execute_query(query, params)
        
        if success:
            for card in cards:
                self.cards_tree.insert('', 'end', values=(
                    card['id'],
                    card['order_number'] or "",
                    card['product_name'] or "",
                    card['part_name'] or "",
                    card['operation_name'] or "",
                    f"{card['actual_time']:.2f}",
                    f"{card['actual_cost']:.2f}",
                    card['operator_name'] or "",
                    card['operation_date'] or "",
                    card['notes'] or ""
                ))
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        self.window.after(500, self.search_operation_cards)

if __name__ == "__main__":
    app = OperationCardsWindow()
    app.window.mainloop()

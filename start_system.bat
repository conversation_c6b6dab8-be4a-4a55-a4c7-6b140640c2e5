@echo off
title Industrial Cost Management System

echo.
echo ========================================
echo    Industrial Cost Management System
echo    نظام التكاليف الصناعي المتكامل
echo ========================================
echo.

REM Check Python installation
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed
    echo Please install Python 3.6 or newer
    echo.
    pause
    exit /b 1
)

echo Python found: 
python --version
echo.

REM Check required files
if not exist "run_system.py" (
    echo Error: run_system.py not found
    echo.
    pause
    exit /b 1
)

echo All files found. Starting system...
echo.

REM Create folders
if not exist "backups" mkdir backups
if not exist "reports" mkdir reports
if not exist "exports" mkdir exports
if not exist "logs" mkdir logs

REM Run the system
python run_system.py

echo.
echo System closed.
pause

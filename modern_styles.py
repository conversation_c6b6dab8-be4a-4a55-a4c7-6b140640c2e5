import tkinter as tk
from tkinter import ttk
import tkinter.font as tkFont

class ModernStyles:
    """فئة الأنماط المودرن للنظام"""
    
    # ألوان النظام المودرن
    COLORS = {
        # الألوان الأساسية
        'primary': '#2c3e50',           # أزرق داكن
        'secondary': '#3498db',         # أزرق فاتح
        'success': '#27ae60',           # أخضر
        'warning': '#f39c12',           # برتقالي
        'danger': '#e74c3c',            # أحمر
        'info': '#17a2b8',              # أزرق فاتح
        
        # ألوان الخلفية
        'bg_primary': '#ecf0f1',        # رمادي فاتح جداً
        'bg_secondary': '#bdc3c7',      # رمادي فاتح
        'bg_dark': '#34495e',           # رمادي داكن
        'bg_light': '#ffffff',          # أبيض
        
        # ألوان النص
        'text_primary': '#2c3e50',      # نص أساسي
        'text_secondary': '#7f8c8d',    # نص ثانوي
        'text_light': '#ffffff',        # نص فاتح
        'text_muted': '#95a5a6',        # نص خافت
        
        # ألوان الحدود
        'border_light': '#bdc3c7',      # حدود فاتحة
        'border_dark': '#7f8c8d',       # حدود داكنة
        
        # ألوان التفاعل
        'hover': '#34495e',             # عند التمرير
        'active': '#2980b9',            # عند النقر
        'focus': '#3498db',             # عند التركيز
        'disabled': '#95a5a6',          # معطل
        
        # ألوان الحالة
        'new': '#3498db',               # جديد
        'in_progress': '#f39c12',       # قيد التنفيذ
        'completed': '#27ae60',         # مكتمل
        'cancelled': '#e74c3c',         # ملغي
        'on_hold': '#95a5a6',           # معلق
    }
    
    # الخطوط المودرن
    FONTS = {
        'title': ('Segoe UI', 16, 'bold'),
        'subtitle': ('Segoe UI', 14, 'bold'),
        'heading': ('Segoe UI', 12, 'bold'),
        'body': ('Segoe UI', 10),
        'small': ('Segoe UI', 9),
        'button': ('Segoe UI', 10, 'bold'),
        'arabic_title': ('Arial', 16, 'bold'),
        'arabic_subtitle': ('Arial', 14, 'bold'),
        'arabic_heading': ('Arial', 12, 'bold'),
        'arabic_body': ('Arial', 10),
        'arabic_small': ('Arial', 9),
    }
    
    # أحجام العناصر
    SIZES = {
        'button_width': 120,
        'button_height': 35,
        'entry_height': 30,
        'padding_small': 5,
        'padding_medium': 10,
        'padding_large': 20,
        'border_radius': 5,
        'icon_size': 16,
    }
    
    @classmethod
    def configure_ttk_styles(cls, root):
        """تكوين أنماط ttk المودرن"""
        style = ttk.Style(root)
        
        # تعيين النمط الأساسي
        style.theme_use('clam')
        
        # تكوين الأنماط المخصصة
        cls._configure_button_styles(style)
        cls._configure_frame_styles(style)
        cls._configure_label_styles(style)
        cls._configure_entry_styles(style)
        cls._configure_treeview_styles(style)
        cls._configure_notebook_styles(style)
        cls._configure_progressbar_styles(style)
        
    @classmethod
    def _configure_button_styles(cls, style):
        """تكوين أنماط الأزرار"""
        # زر أساسي
        style.configure('Primary.TButton',
                       background=cls.COLORS['primary'],
                       foreground=cls.COLORS['text_light'],
                       font=cls.FONTS['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(10, 8))
        
        style.map('Primary.TButton',
                 background=[('active', cls.COLORS['hover']),
                           ('pressed', cls.COLORS['active'])])
        
        # زر ثانوي
        style.configure('Secondary.TButton',
                       background=cls.COLORS['secondary'],
                       foreground=cls.COLORS['text_light'],
                       font=cls.FONTS['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(10, 8))
        
        style.map('Secondary.TButton',
                 background=[('active', cls.COLORS['info']),
                           ('pressed', cls.COLORS['active'])])
        
        # زر النجاح
        style.configure('Success.TButton',
                       background=cls.COLORS['success'],
                       foreground=cls.COLORS['text_light'],
                       font=cls.FONTS['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(10, 8))
        
        # زر التحذير
        style.configure('Warning.TButton',
                       background=cls.COLORS['warning'],
                       foreground=cls.COLORS['text_light'],
                       font=cls.FONTS['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(10, 8))
        
        # زر الخطر
        style.configure('Danger.TButton',
                       background=cls.COLORS['danger'],
                       foreground=cls.COLORS['text_light'],
                       font=cls.FONTS['button'],
                       borderwidth=0,
                       focuscolor='none',
                       padding=(10, 8))
        
    @classmethod
    def _configure_frame_styles(cls, style):
        """تكوين أنماط الإطارات"""
        # إطار أساسي
        style.configure('Card.TFrame',
                       background=cls.COLORS['bg_light'],
                       relief='flat',
                       borderwidth=1)
        
        # إطار داكن
        style.configure('Dark.TFrame',
                       background=cls.COLORS['bg_dark'])
        
        # إطار الشريط الجانبي
        style.configure('Sidebar.TFrame',
                       background=cls.COLORS['primary'])
        
    @classmethod
    def _configure_label_styles(cls, style):
        """تكوين أنماط التسميات"""
        # تسمية العنوان
        style.configure('Title.TLabel',
                       background=cls.COLORS['bg_light'],
                       foreground=cls.COLORS['text_primary'],
                       font=cls.FONTS['title'])
        
        # تسمية العنوان الفرعي
        style.configure('Subtitle.TLabel',
                       background=cls.COLORS['bg_light'],
                       foreground=cls.COLORS['text_primary'],
                       font=cls.FONTS['subtitle'])
        
        # تسمية الرأس
        style.configure('Heading.TLabel',
                       background=cls.COLORS['bg_light'],
                       foreground=cls.COLORS['text_primary'],
                       font=cls.FONTS['heading'])
        
        # تسمية الشريط الجانبي
        style.configure('Sidebar.TLabel',
                       background=cls.COLORS['primary'],
                       foreground=cls.COLORS['text_light'],
                       font=cls.FONTS['heading'])
        
    @classmethod
    def _configure_entry_styles(cls, style):
        """تكوين أنماط حقول الإدخال"""
        style.configure('Modern.TEntry',
                       fieldbackground=cls.COLORS['bg_light'],
                       borderwidth=1,
                       relief='solid',
                       insertcolor=cls.COLORS['primary'],
                       font=cls.FONTS['body'])
        
        style.map('Modern.TEntry',
                 focuscolor=[('!focus', cls.COLORS['border_light']),
                           ('focus', cls.COLORS['focus'])])
        
    @classmethod
    def _configure_treeview_styles(cls, style):
        """تكوين أنماط جدول البيانات"""
        style.configure('Modern.Treeview',
                       background=cls.COLORS['bg_light'],
                       foreground=cls.COLORS['text_primary'],
                       fieldbackground=cls.COLORS['bg_light'],
                       borderwidth=1,
                       relief='solid',
                       font=cls.FONTS['body'])
        
        style.configure('Modern.Treeview.Heading',
                       background=cls.COLORS['primary'],
                       foreground=cls.COLORS['text_light'],
                       font=cls.FONTS['heading'],
                       relief='flat')
        
        style.map('Modern.Treeview',
                 background=[('selected', cls.COLORS['secondary'])],
                 foreground=[('selected', cls.COLORS['text_light'])])
        
    @classmethod
    def _configure_notebook_styles(cls, style):
        """تكوين أنماط التبويبات"""
        style.configure('Modern.TNotebook',
                       background=cls.COLORS['bg_primary'],
                       borderwidth=0)
        
        style.configure('Modern.TNotebook.Tab',
                       background=cls.COLORS['bg_secondary'],
                       foreground=cls.COLORS['text_primary'],
                       font=cls.FONTS['heading'],
                       padding=(20, 10))
        
        style.map('Modern.TNotebook.Tab',
                 background=[('selected', cls.COLORS['primary']),
                           ('active', cls.COLORS['hover'])],
                 foreground=[('selected', cls.COLORS['text_light']),
                           ('active', cls.COLORS['text_light'])])
        
    @classmethod
    def _configure_progressbar_styles(cls, style):
        """تكوين أنماط شريط التقدم"""
        style.configure('Modern.TProgressbar',
                       background=cls.COLORS['secondary'],
                       troughcolor=cls.COLORS['bg_secondary'],
                       borderwidth=0,
                       lightcolor=cls.COLORS['secondary'],
                       darkcolor=cls.COLORS['secondary'])
        
    @classmethod
    def create_modern_button(cls, parent, text, command=None, style='Primary', **kwargs):
        """إنشاء زر مودرن"""
        button = ttk.Button(parent, text=text, command=command, style=f'{style}.TButton', **kwargs)
        return button
        
    @classmethod
    def create_modern_frame(cls, parent, style='Card', **kwargs):
        """إنشاء إطار مودرن"""
        frame = ttk.Frame(parent, style=f'{style}.TFrame', **kwargs)
        return frame
        
    @classmethod
    def create_modern_label(cls, parent, text, style='Heading', **kwargs):
        """إنشاء تسمية مودرن"""
        label = ttk.Label(parent, text=text, style=f'{style}.TLabel', **kwargs)
        return label
        
    @classmethod
    def create_modern_entry(cls, parent, textvariable=None, **kwargs):
        """إنشاء حقل إدخال مودرن"""
        entry = ttk.Entry(parent, textvariable=textvariable, style='Modern.TEntry', **kwargs)
        return entry
        
    @classmethod
    def create_modern_treeview(cls, parent, columns, **kwargs):
        """إنشاء جدول بيانات مودرن"""
        tree = ttk.Treeview(parent, columns=columns, style='Modern.Treeview', **kwargs)
        return tree
        
    @classmethod
    def create_modern_notebook(cls, parent, **kwargs):
        """إنشاء تبويبات مودرن"""
        notebook = ttk.Notebook(parent, style='Modern.TNotebook', **kwargs)
        return notebook
        
    @classmethod
    def apply_hover_effect(cls, widget, enter_color=None, leave_color=None):
        """تطبيق تأثير التمرير على العنصر"""
        if enter_color is None:
            enter_color = cls.COLORS['hover']
        if leave_color is None:
            leave_color = cls.COLORS['primary']
            
        def on_enter(event):
            widget.configure(background=enter_color)
            
        def on_leave(event):
            widget.configure(background=leave_color)
            
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
        
    @classmethod
    def create_gradient_frame(cls, parent, color1, color2, width, height):
        """إنشاء إطار بتدرج لوني"""
        canvas = tk.Canvas(parent, width=width, height=height, highlightthickness=0)
        
        # إنشاء التدرج
        for i in range(height):
            ratio = i / height
            r1, g1, b1 = cls._hex_to_rgb(color1)
            r2, g2, b2 = cls._hex_to_rgb(color2)
            
            r = int(r1 + (r2 - r1) * ratio)
            g = int(g1 + (g2 - g1) * ratio)
            b = int(b1 + (b2 - b1) * ratio)
            
            color = f"#{r:02x}{g:02x}{b:02x}"
            canvas.create_line(0, i, width, i, fill=color)
            
        return canvas
        
    @classmethod
    def _hex_to_rgb(cls, hex_color):
        """تحويل اللون من hex إلى RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        
    @classmethod
    def create_card_frame(cls, parent, title=None, **kwargs):
        """إنشاء إطار بشكل بطاقة"""
        # الإطار الخارجي
        outer_frame = tk.Frame(parent, bg=cls.COLORS['bg_primary'], **kwargs)
        
        # الإطار الداخلي (البطاقة)
        card_frame = tk.Frame(outer_frame, 
                             bg=cls.COLORS['bg_light'],
                             relief='flat',
                             bd=1,
                             highlightbackground=cls.COLORS['border_light'],
                             highlightthickness=1)
        card_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        # العنوان إذا كان موجود
        if title:
            title_frame = tk.Frame(card_frame, bg=cls.COLORS['primary'], height=40)
            title_frame.pack(fill='x')
            title_frame.pack_propagate(False)
            
            title_label = tk.Label(title_frame, 
                                 text=title,
                                 bg=cls.COLORS['primary'],
                                 fg=cls.COLORS['text_light'],
                                 font=cls.FONTS['heading'])
            title_label.pack(expand=True)
            
        return outer_frame, card_frame
        
    @classmethod
    def create_status_badge(cls, parent, text, status='new'):
        """إنشاء شارة حالة"""
        color_map = {
            'new': cls.COLORS['new'],
            'in_progress': cls.COLORS['warning'],
            'completed': cls.COLORS['success'],
            'cancelled': cls.COLORS['danger'],
            'on_hold': cls.COLORS['disabled']
        }
        
        bg_color = color_map.get(status, cls.COLORS['new'])
        
        badge = tk.Label(parent,
                        text=text,
                        bg=bg_color,
                        fg=cls.COLORS['text_light'],
                        font=cls.FONTS['small'],
                        padx=8,
                        pady=2)
        
        return badge

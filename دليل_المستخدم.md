# دليل المستخدم - نظام التكاليف الصناعي المتكامل

## 📖 مقدمة

مرحباً بك في نظام التكاليف الصناعي المتكامل، نظام محاسبة صناعية شامل مصمم لإدارة التكاليف والإنتاج في المؤسسات الصناعية.

## 🚀 البدء السريع

### 1. تشغيل النظام
```bash
# الطريقة الأولى: استخدام ملف التشغيل
python run_system.py

# الطريقة الثانية: استخدام ملف batch (Windows)
تشغيل_النظام.bat

# الطريقة الثالثة: تشغيل مباشر
python main_window.py
```

### 2. الشاشة الرئيسية
عند تشغيل النظام ستظهر:
- شاشة البداية (3 ثواني)
- النافذة الرئيسية مع القوائم

### 3. اختيار السنة المالية
- في أعلى النافذة، اختر السنة المالية المطلوبة
- السنة الافتراضية: 2024/2025

## 📋 الوحدات الرئيسية

### 🔧 البيانات الأساسية

#### إدارة المنتجات
**الوصول**: البيانات الأساسية ← المنتجات

**الوظائف المتاحة**:
- ✅ إضافة منتج جديد
- ✅ تعديل منتج موجود
- ✅ حذف منتج
- ✅ البحث في المنتجات
- ✅ عرض قائمة المنتجات

**البيانات المطلوبة**:
- **اسم المنتج**: (إجباري) اسم المنتج
- **كود المنتج**: (اختياري) رمز تعريفي للمنتج
- **الوصف**: (اختياري) وصف تفصيلي
- **الوحدة**: (إجباري) وحدة القياس (قطعة، كيلو، متر، إلخ)
- **التكلفة المعيارية**: (اختياري) التكلفة المتوقعة
- **نشط**: حالة المنتج (نشط/غير نشط)

**خطوات إضافة منتج**:
1. انقر على "البيانات الأساسية"
2. اختر "المنتجات"
3. أدخل بيانات المنتج في النموذج
4. انقر "إضافة"

**خطوات تعديل منتج**:
1. ابحث عن المنتج أو اختره من القائمة
2. ستظهر بياناته في النموذج
3. عدل البيانات المطلوبة
4. انقر "تحديث"

#### إدارة الأجزاء
**الحالة**: قيد التطوير
**الوصف**: إدارة أجزاء المنتجات والمكونات

#### إدارة القطاعات
**الحالة**: قيد التطوير
**الوصف**: تقسيم المصنع إلى قطاعات إنتاجية

#### إدارة الورش
**الحالة**: قيد التطوير
**الوصف**: إدارة ورش العمل والمعدات

#### عمليات التشغيل
**الحالة**: قيد التطوير
**الوصف**: تعريف عمليات التصنيع والتشغيل

### 📊 بيانات الحركة

#### أوامر التشغيل
**الحالة**: قيد التطوير
**الوصف**: إنشاء وإدارة أوامر الإنتاج

#### بطاقة التشغيل
**الحالة**: قيد التطوير
**الوصف**: تسجيل تفاصيل عمليات التشغيل

#### صرف الخامة
**الحالة**: قيد التطوير
**الوصف**: تسجيل استهلاك المواد الخام

#### القطاعات غير السليمة
**الحالة**: قيد التطوير
**الوصف**: تسجيل المنتجات المعيبة والهالك

### 🔍 الاستعلام
**الحالة**: قيد التطوير
**الوصف**: استعلامات متقدمة عن البيانات

### 📈 التقارير
**الحالة**: قيد التطوير
**الوصف**: تقارير شاملة عن التكاليف والإنتاج

### 💾 النسخ الاحتياطي

#### إنشاء نسخة احتياطية
1. انقر على "نسخة احتياطية"
2. اختر "إنشاء نسخة احتياطية"
3. سيتم حفظ النسخة في مجلد `backups`
4. اسم الملف يحتوي على التاريخ والوقت

#### استرجاع نسخة احتياطية
1. انقر على "نسخة احتياطية"
2. اختر "استرجاع نسخة احتياطية"
3. اختر ملف النسخة الاحتياطية
4. أكد العملية (سيتم استبدال البيانات الحالية)

## 🔧 نصائح الاستخدام

### البحث والتصفية
- استخدم مربع البحث للعثور على البيانات بسرعة
- البحث يعمل في جميع الحقول النصية
- البحث تلقائي أثناء الكتابة

### إدارة البيانات
- تأكد من حفظ البيانات قبل الانتقال لنافذة أخرى
- استخدم زر "مسح" لتنظيف النموذج
- تأكد من صحة البيانات قبل الحفظ

### الأمان
- أنشئ نسخة احتياطية بانتظام
- لا تحذف البيانات إلا عند التأكد
- احتفظ بنسخ احتياطية في مكان آمن

## ⚠️ رسائل الخطأ الشائعة

### "يجب إدخال اسم المنتج"
**السبب**: لم يتم إدخال اسم المنتج
**الحل**: أدخل اسم المنتج في الحقل المطلوب

### "يجب اختيار وحدة المنتج"
**السبب**: لم يتم اختيار وحدة القياس
**الحل**: اختر وحدة من القائمة المنسدلة

### "التكلفة المعيارية يجب أن تكون رقم صحيح"
**السبب**: تم إدخال قيمة غير صحيحة في حقل التكلفة
**الحل**: أدخل رقم صحيح (مثل: 100 أو 150.50)

### "فشل في الاتصال بقاعدة البيانات"
**السبب**: مشكلة في قاعدة البيانات
**الحل**: 
1. أعد تشغيل النظام
2. تأكد من وجود صلاحيات الكتابة في المجلد
3. احذف ملف `industrial_costs.db` وأعد التشغيل

## 🛠️ استكشاف الأخطاء

### النظام لا يبدأ
1. تأكد من تثبيت Python 3.6 أو أحدث
2. تأكد من وجود جميع الملفات في نفس المجلد
3. شغل الأمر: `python test_system.py --quick`

### النوافذ لا تظهر بشكل صحيح
1. تأكد من دعم النظام للخطوط العربية
2. جرب تشغيل النظام كمدير
3. تأكد من دقة الشاشة المناسبة

### بطء في الأداء
1. أغلق البرامج غير المستخدمة
2. أنشئ نسخة احتياطية وأعد تشغيل النظام
3. تأكد من توفر مساحة كافية على القرص الصلب

## 📞 الحصول على المساعدة

### الأوامر المفيدة
```bash
# عرض معلومات النظام
python run_system.py --version

# عرض المساعدة
python run_system.py --help

# اختبار النظام
python test_system.py

# اختبار سريع
python test_system.py --quick

# معلومات النظام
python test_system.py --info
```

### ملفات السجلات
- مجلد `logs`: يحتوي على سجلات النظام
- مجلد `backups`: يحتوي على النسخ الاحتياطية
- مجلد `reports`: سيحتوي على التقارير (قريباً)

## 🔄 التحديثات المستقبلية

### الإصدار القادم (1.1)
- [ ] إكمال نوافذ البيانات الأساسية
- [ ] تطوير نظام أوامر التشغيل
- [ ] إضافة التقارير الأساسية

### الإصدار المتوسط (1.5)
- [ ] نظام المستخدمين والصلاحيات
- [ ] واجهة الاستعلامات المتقدمة
- [ ] الرسوم البيانية

### الإصدار المتقدم (2.0)
- [ ] تطبيق ويب
- [ ] تكامل مع أنظمة أخرى
- [ ] تقارير متقدمة

---

**ملاحظة**: هذا الدليل يغطي الإصدار الحالي (1.0). سيتم تحديثه مع إضافة المزيد من المميزات.

للدعم الفني أو الاستفسارات، يرجى مراجعة ملف `README.md` أو استخدام أوامر المساعدة المذكورة أعلاه.

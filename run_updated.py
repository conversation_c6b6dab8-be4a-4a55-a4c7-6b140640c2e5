#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النسخة المحدثة من النظام
Run the updated version of the system

هذا الملف يشغل النسخة المحدثة مباشرة بالواجهة المودرن
This file runs the updated version directly with modern interface
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل النسخة المحدثة"""
    try:
        print("🏭 نظام التكاليف الصناعي المتكامل - النسخة المحدثة")
        print("=" * 60)
        print("المميزات الجديدة:")
        print("✅ ربط المنتجات بالأجزاء (BOM)")
        print("✅ واجهة مودرن واحترافية") 
        print("✅ تنقلات محسنة")
        print("✅ لوحة معلومات تفاعلية")
        print("=" * 60)
        print("🚀 جاري تشغيل النظام...")
        print("=" * 60)
        
        # استيراد الوحدات المطلوبة
        from modern_main_window import ModernMainWindow
        from database import DatabaseManager
        
        # تهيئة قاعدة البيانات
        print("🔧 جاري تهيئة قاعدة البيانات...")
        db = DatabaseManager()
        success, message = db.initialize_database()
        
        if not success:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {message}")
            input("اضغط Enter للخروج...")
            return
        
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        print("🎨 جاري تحميل الواجهة المودرن...")
        
        # تشغيل النظام
        app = ModernMainWindow()
        app.run()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("\nالملفات المطلوبة:")
        print("• modern_main_window.py")
        print("• modern_styles.py") 
        print("• database.py")
        print("• config.py")
        print("• product_parts_window.py")
        print("\nتأكد من وجود جميع الملفات في نفس المجلد")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("يرجى مراجعة ملفات السجلات أو الاتصال بالدعم الفني")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
